"use strict";exports.id=791,exports.ids=[791],exports.modules={6791:e=>{e.exports=JSON.parse('{"title":"Convertisseur WebM Billie","subtitle":"Convertissez des vid\xe9os MP4 au format WebM avec redimensionnement 512x512","steps":{"step1":"\xc9tape 1 : T\xe9l\xe9chargez votre fichier MP4","step2":"\xc9tape 2 : Attendez la conversion automatique","step3":"\xc9tape 3 : T\xe9l\xe9chargez votre nouveau fichier WebM"},"upload":{"dragDrop":"Glissez et d\xe9posez votre fichier vid\xe9o ici","or":"ou","clickToSelect":"Cliquez pour s\xe9lectionner un fichier","maxSize":"Taille maximale : 100 Mo","supportedFormats":"Supporte MP4, <PERSON><PERSON>, <PERSON>O<PERSON>, MK<PERSON>, WebM et plus","uploading":"T\xe9l\xe9chargement en cours...","processing":"Conversion de la vid\xe9o...","success":"Conversion termin\xe9e !","downloadReady":"Votre fichier WebM est pr\xeat \xe0 \xeatre t\xe9l\xe9charg\xe9"},"buttons":{"selectFile":"S\xe9lectionner un fichier","download":"T\xe9l\xe9charger WebM","convertAnother":"Convertir un autre fichier","cancel":"Annuler"},"errors":{"unsupportedFormat":"Format vid\xe9o non support\xe9. Veuillez t\xe9l\xe9charger un fichier vid\xe9o commun (MP4, AVI, MOV, etc.)","fileTooLarge":"Fichier trop volumineux. Veuillez t\xe9l\xe9charger une vid\xe9o de moins de 100 Mo.","conversionFailed":"La conversion a \xe9chou\xe9. Veuillez r\xe9essayer.","uploadFailed":"Le t\xe9l\xe9chargement a \xe9chou\xe9. Veuillez r\xe9essayer.","noFileSelected":"Aucun fichier s\xe9lectionn\xe9. Veuillez choisir un fichier vid\xe9o."},"language":{"switch":"Passer \xe0 l\'anglais","current":"Fran\xe7ais"},"progress":{"uploading":"T\xe9l\xe9chargement : {progress}%","converting":"Conversion : {progress}%","preparing":"Pr\xe9paration de la conversion...","finalizing":"Finalisation..."}}')}};