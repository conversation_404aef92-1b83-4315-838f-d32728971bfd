{"title": "<PERSON>M Converter", "subtitle": "Convert MP4 videos to WebM format with 512x512 resizing", "steps": {"step1": "Step 1: Upload your MP4 file", "step2": "Step 2: Wait for automatic conversion", "step3": "Step 3: Download your new WebM file"}, "upload": {"dragDrop": "Drag and drop your video file here", "or": "or", "clickToSelect": "Click to select file", "maxSize": "Max file size: 100MB", "supportedFormats": "Supports MP4, AVI, MOV, MKV, WebM and more", "uploading": "Uploading...", "processing": "Converting video...", "success": "Conversion complete!", "downloadReady": "Your WebM file is ready for download"}, "buttons": {"selectFile": "Select File", "download": "Download WebM", "convertAnother": "Convert Another File", "cancel": "Cancel"}, "errors": {"unsupportedFormat": "Unsupported video format. Please upload a common video file (MP4, AVI, MOV, etc.)", "fileTooLarge": "File size too large. Please upload a video smaller than 100MB.", "conversionFailed": "Conversion failed. Please try again.", "uploadFailed": "Upload failed. Please try again.", "noFileSelected": "No file selected. Please choose a video file."}, "language": {"switch": "Switch to French", "current": "English"}, "progress": {"uploading": "Uploading: {progress}%", "converting": "Converting: {progress}%", "preparing": "Preparing conversion...", "finalizing": "Finalizing..."}}