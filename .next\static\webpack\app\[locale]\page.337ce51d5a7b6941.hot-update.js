"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VideoConverter() {\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conversion, setConversion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stage: 'idle',\n        progress: 0\n    });\n    const [convertedVideo, setConvertedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ffmpegLoaded, setFfmpegLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ffmpegRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const switchLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[switchLocale]\": ()=>{\n            const newLocale = locale === 'en' ? 'fr' : 'en';\n            const newPath = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n            router.push(newPath);\n        }\n    }[\"VideoConverter.useCallback[switchLocale]\"], [\n        locale,\n        pathname,\n        router\n    ]);\n    const loadFFmpeg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[loadFFmpeg]\": async ()=>{\n            if (ffmpegRef.current || ffmpegLoaded) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 10\n                });\n                const ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__.FFmpeg();\n                ffmpegRef.current = ffmpeg;\n                // Load FFmpeg with progress tracking\n                ffmpeg.on('log', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { message } = param;\n                        console.log('FFmpeg log:', message);\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                ffmpeg.on('progress', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { progress } = param;\n                        if (conversion.stage === 'converting') {\n                            const adjustedProgress = 50 + progress * 40 // 50-90%\n                            ;\n                            setConversion({\n                                \"VideoConverter.useCallback[loadFFmpeg]\": (prev)=>({\n                                        ...prev,\n                                        progress: adjustedProgress\n                                    })\n                            }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                        }\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                setConversion({\n                    stage: 'loading',\n                    progress: 30\n                });\n                // Load FFmpeg core with more stable version\n                const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.4/dist/umd';\n                await ffmpeg.load({\n                    coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n                    wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm'),\n                    workerURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.worker.js\"), 'text/javascript')\n                });\n                setFfmpegLoaded(true);\n                setConversion({\n                    stage: 'idle',\n                    progress: 0\n                });\n            } catch (error) {\n                console.error('Failed to load FFmpeg:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[loadFFmpeg]\"], [\n        ffmpegLoaded,\n        conversion.stage\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileSelect]\": (selectedFile)=>{\n            // Validate file type\n            const allowedTypes = [\n                'video/mp4',\n                'video/avi',\n                'video/mov',\n                'video/quicktime',\n                'video/x-msvideo',\n                'video/mkv',\n                'video/x-matroska',\n                'video/webm',\n                'video/3gpp',\n                'video/x-flv',\n                'video/x-ms-wmv'\n            ];\n            const allowedExtensions = [\n                '.mp4',\n                '.avi',\n                '.mov',\n                '.mkv',\n                '.webm',\n                '.3gp',\n                '.flv',\n                '.wmv',\n                '.m4v',\n                '.mpg',\n                '.mpeg',\n                '.ogv'\n            ];\n            const fileName = selectedFile.name.toLowerCase();\n            const hasValidType = allowedTypes.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidType\": (type)=>selectedFile.type.includes(type)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidType\"]);\n            const hasValidExtension = allowedExtensions.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidExtension\": (ext)=>fileName.endsWith(ext)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidExtension\"]);\n            if (!hasValidType && !hasValidExtension) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.unsupportedFormat')\n                });\n                return;\n            }\n            // Check file size (100MB limit)\n            if (selectedFile.size > 100 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.fileTooLarge')\n                });\n                return;\n            }\n            setFile(selectedFile);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n        }\n    }[\"VideoConverter.useCallback[handleFileSelect]\"], []);\n    const convertVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[convertVideo]\": async ()=>{\n            if (!file || !ffmpegRef.current) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 20\n                });\n                const ffmpeg = ffmpegRef.current;\n                // Write input file\n                await ffmpeg.writeFile('input.mp4', await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.fetchFile)(file));\n                setConversion({\n                    stage: 'converting',\n                    progress: 50\n                });\n                // Convert video with simpler, more memory-efficient settings\n                await ffmpeg.exec([\n                    '-i',\n                    'input.mp4',\n                    '-vf',\n                    'scale=512:512:force_original_aspect_ratio=decrease,pad=512:512:(ow-iw)/2:(oh-ih)/2:black',\n                    '-c:v',\n                    'libvpx',\n                    '-crf',\n                    '35',\n                    '-b:v',\n                    '500k',\n                    '-c:a',\n                    'libvorbis',\n                    '-b:a',\n                    '96k',\n                    '-f',\n                    'webm',\n                    '-threads',\n                    '1',\n                    'output.webm'\n                ]);\n                setConversion({\n                    stage: 'converting',\n                    progress: 95\n                });\n                // Read output file\n                const data = await ffmpeg.readFile('output.webm');\n                const blob = new Blob([\n                    data\n                ], {\n                    type: 'video/webm'\n                });\n                setConvertedVideo(blob);\n                setConversion({\n                    stage: 'completed',\n                    progress: 100\n                });\n                // Clean up\n                await ffmpeg.deleteFile('input.mp4');\n                await ffmpeg.deleteFile('output.webm');\n            } catch (error) {\n                console.error('Conversion error:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[convertVideo]\"], [\n        file\n    ]);\n    const downloadVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[downloadVideo]\": ()=>{\n            if (!convertedVideo) return;\n            const url = URL.createObjectURL(convertedVideo);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"converted-\".concat(Date.now(), \".webm\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"VideoConverter.useCallback[downloadVideo]\"], [\n        convertedVideo\n    ]);\n    const resetConverter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[resetConverter]\": ()=>{\n            setFile(null);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n        }\n    }[\"VideoConverter.useCallback[resetConverter]\"], []);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"VideoConverter.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"VideoConverter.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            if (droppedFiles.length > 0) {\n                handleFileSelect(droppedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleDrop]\"], [\n        handleFileSelect\n    ]);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileInputChange]\": (e)=>{\n            const selectedFiles = e.target.files;\n            if (selectedFiles && selectedFiles.length > 0) {\n                handleFileSelect(selectedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleFileInputChange]\"], [\n        handleFileSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: t('subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: switchLocale,\n                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            t('language.switch')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            !file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-area p-8 text-center cursor-pointer \".concat(isDragOver ? 'dragover' : ''),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                        children: t('upload.dragDrop')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                        children: t('upload.or')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 dark:text-blue-400 mb-4\",\n                        children: t('upload.clickToSelect')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: t('upload.maxSize')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"video/*\",\n                        onChange: handleFileInputChange,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, this),\n            file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Selected File:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-500\",\n                                children: [\n                                    \"Size: \",\n                                    (file.size / (1024 * 1024)).toFixed(2),\n                                    \" MB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            conversion.stage === 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ffmpegLoaded ? convertVideo : loadFFmpeg,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.cancel')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadVideo,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.download')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.convertAnother')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this),\n            (conversion.stage === 'loading' || conversion.stage === 'converting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    Math.round(conversion.progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-bar bg-blue-600 h-2 rounded-full\",\n                            style: {\n                                width: \"\".concat(conversion.progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 dark:text-red-200\",\n                                    children: \"Conversion Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600 dark:text-red-300 mt-1\",\n                                    children: conversion.error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetConverter,\n                                    className: \"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-3 w-3 text-white\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-green-800 dark:text-green-200\",\n                                    children: t('upload.success')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600 dark:text-green-300\",\n                                    children: t('upload.downloadReady')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 373,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoConverter, \"yK1HBG1EYjIXbihU4jvy68Slolw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = VideoConverter;\nvar _c;\n$RefreshReg$(_c, \"VideoConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoConverter.tsx\n"));

/***/ })

});