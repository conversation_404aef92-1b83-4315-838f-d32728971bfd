{"name": "billie-webm-converter", "version": "0.1.0", "description": "Billie WebM Converter - A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "autoprefixer": "^10.4.21", "lucide-react": "^0.468.0", "next": "^15.3.4", "next-intl": "^3.22.4", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "eslint": "^8.57.1", "eslint-config-next": "^15.3.4", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}