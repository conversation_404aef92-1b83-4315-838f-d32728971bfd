/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Fredoka', cursive;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    color: #2d3748;
    overflow-x: hidden;
    position: relative;
}

/* Floating bubbles background animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="80" r="2.5" fill="rgba(255,255,255,0.08)"/><circle cx="10" cy="60" r="1.2" fill="rgba(255,255,255,0.12)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
    z-index: 1;
}

@keyframes float {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

/* Magical floating elements */
.magic-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.magic-element {
    position: absolute;
    font-size: 2rem;
    animation: magicFloat 15s infinite linear;
    opacity: 0.6;
}

.magic-element:nth-child(1) { left: 10%; animation-delay: 0s; }
.magic-element:nth-child(2) { left: 20%; animation-delay: -2s; }
.magic-element:nth-child(3) { left: 30%; animation-delay: -4s; }
.magic-element:nth-child(4) { left: 40%; animation-delay: -6s; }
.magic-element:nth-child(5) { left: 50%; animation-delay: -8s; }
.magic-element:nth-child(6) { left: 60%; animation-delay: -10s; }
.magic-element:nth-child(7) { left: 70%; animation-delay: -12s; }
.magic-element:nth-child(8) { left: 80%; animation-delay: -14s; }

@keyframes magicFloat {
    0% {
        transform: translateY(100vh) rotate(0deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg) scale(1.2);
        opacity: 0;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Billie Logo Header */
.header {
    padding: 1rem 0 2rem 0;
    text-align: center;
    position: relative;
}

.billie-logo {
    width: 200px;
    height: auto;
    margin: 0 auto 1rem auto;
    display: block;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.1));
    animation: bounce 3s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.billie-logo:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 15px 30px rgba(0,0,0,0.2))
            drop-shadow(0 0 20px rgba(255,182,193,0.6));
    animation: bounce 1s ease-in-out infinite, wiggle 0.5s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.title-section {
    text-align: center;
}

.title-section h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.2);
    animation: wiggle 4s ease-in-out infinite;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    75% { transform: rotate(-1deg); }
}

.title-section p {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 400;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border: none;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.language-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.language-toggle:hover::before {
    left: 100%;
}

.language-toggle:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 25px rgba(255, 107, 107, 0.4);
}

.language-toggle:active {
    transform: translateY(-1px) scale(1.02);
}

/* Main Content */
.main-content {
    padding-bottom: 3rem;
}

/* Instructions */
.instructions {
    margin-bottom: 3rem;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.step {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.8));
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 2.5rem;
    text-align: center;
    border: 3px solid rgba(255, 255, 255, 0.6);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.step::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,182,193,0.1) 0%, transparent 70%);
    animation: sparkle 4s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.5; }
    50% { transform: rotate(180deg) scale(1.1); opacity: 0.8; }
}

.step:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #feca57, #48cae4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin: 0 auto 1.5rem auto;
    box-shadow: 0 12px 25px rgba(255, 107, 107, 0.4);
    animation: pulse 2s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.step h3 {
    color: #2d3748;
    font-size: 1.4rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

/* Conversion Interface */
.conversion-interface {
    display: flex;
    justify-content: center;
}

.upload-section,
.preview-section,
.progress-section,
.download-section {
    width: 100%;
    max-width: 500px;
}

/* Upload Area */
.upload-area {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    border: 4px dashed #ff9a9e;
    border-radius: 30px;
    padding: 4rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(15px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255,182,193,0.1), transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(173,216,230,0.1), transparent 50%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, rgba(255,255,255,1), rgba(255,240,245,0.9));
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 30px 60px rgba(255,107,107,0.2);
}

.upload-icon {
    font-size: 5rem;
    color: #ff9a9e;
    margin-bottom: 1.5rem;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.upload-area:hover .upload-icon,
.upload-area.drag-over .upload-icon {
    color: #ff6b6b;
    animation: bounce 1s infinite, rotate 2s ease-in-out infinite;
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.upload-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.upload-subtext {
    color: #4a5568;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.click-text {
    color: #667eea;
    font-weight: 600;
}

.upload-info {
    font-size: 0.9rem;
    color: #999;
}

.upload-info p {
    margin: 0.25rem 0;
}

/* Preview Section */
.preview-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.preview-video {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    background: #f5f5f5;
    margin-bottom: 1rem;
}

.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-details i {
    font-size: 1.5rem;
    color: #667eea;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.9rem;
    color: #666;
}

.clear-button {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    color: white;
    font-weight: bold;
}

.clear-button:hover {
    background: linear-gradient(135deg, #ff6b6b, #ff9a9e);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.5);
}

.clear-button:active {
    transform: scale(0.95) rotate(90deg);
}

/* Progress Section */
.progress-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.progress-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255,255,255,0.3);
    border-radius: 50px;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #ff6b6b, #feca57, #48cae4, #4ecdc4);
    background-size: 200% 200%;
    border-radius: 50px;
    transition: width 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 0%;
    animation: gradient-shift 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
}

/* Download Section */
.download-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    backdrop-filter: blur(15px);
    border-radius: 30px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
}

.download-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(76,175,80,0.1) 0%, transparent 70%);
    animation: celebration 3s ease-in-out infinite;
}

@keyframes celebration {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
    50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
}

.success-icon {
    font-size: 5rem;
    color: #4caf50;
    margin-bottom: 1.5rem;
    animation: bounce 2s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
    position: relative;
    z-index: 2;
}

@keyframes glow {
    0% { text-shadow: 0 0 10px rgba(76, 175, 80, 0.5); }
    100% { text-shadow: 0 0 20px rgba(76, 175, 80, 0.8), 0 0 30px rgba(76, 175, 80, 0.6); }
}

.download-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.download-card p {
    color: #666;
    margin-bottom: 2rem;
}

.download-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.download-button,
.convert-another-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1.2rem 2.5rem;
    border: none;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.download-button::before,
.convert-another-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.download-button:hover::before,
.convert-another-button:hover::before {
    left: 100%;
}

.download-button {
    background: linear-gradient(135deg, #4caf50, #45a049, #66bb6a);
    color: white;
}

.download-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 30px rgba(76, 175, 80, 0.4);
}

.convert-another-button {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: #2d3748;
}

.convert-another-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 30px rgba(255, 154, 158, 0.4);
}

/* Error Message */
.error-message {
    background: #ffebee;
    border: 1px solid #ffcdd2;
    color: #c62828;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    text-align: left;
    white-space: pre-line;
    line-height: 1.5;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .title-section h1 {
        font-size: 2rem;
    }
    
    .header-content {
        text-align: center;
    }
    
    .steps {
        grid-template-columns: 1fr;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .download-buttons {
        flex-direction: column;
    }
    
    .language-toggle span {
        display: none;
    }
}
