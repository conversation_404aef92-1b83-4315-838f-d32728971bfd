import type { Metadata, Viewport } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Billie WebM Converter',
  description: 'A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing',
  keywords: 'video converter, webm, ffmpeg, online converter, video compression',
  authors: [{ name: 'Billie WebM Converter' }],
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
