"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_en_json";
exports.ids = ["_rsc_messages_en_json"];
exports.modules = {

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"title":"Billie WebM Converter","subtitle":"Convert MP4 videos to WebM format with 512x512 resizing","steps":{"step1":"Step 1: Upload your MP4 file","step2":"Step 2: Wait for automatic conversion","step3":"Step 3: Download your new WebM file"},"upload":{"dragDrop":"Drag and drop your video file here","or":"or","clickToSelect":"Click to select file","maxSize":"Max file size: 50MB","supportedFormats":"Supports MP4, AVI, MOV, MKV, WebM and more","uploading":"Uploading...","processing":"Converting video...","success":"Conversion complete!","downloadReady":"Your WebM file is ready for download"},"buttons":{"selectFile":"Select File","download":"Download WebM","convertAnother":"Convert Another File","cancel":"Cancel"},"errors":{"unsupportedFormat":"Unsupported video format. Please upload a common video file (MP4, AVI, MOV, etc.)","fileTooLarge":"File size too large. Please upload a video smaller than 50MB.","conversionFailed":"Conversion failed. Please try again.","uploadFailed":"Upload failed. Please try again.","noFileSelected":"No file selected. Please choose a video file."},"language":{"switch":"Switch to French","current":"English"},"progress":{"uploading":"Uploading: {progress}%","converting":"Converting: {progress}%","preparing":"Preparing conversion...","finalizing":"Finalizing..."}}');

/***/ })

};
;