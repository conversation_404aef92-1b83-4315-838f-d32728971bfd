"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VideoConverter() {\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conversion, setConversion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stage: 'idle',\n        progress: 0\n    });\n    const [convertedVideo, setConvertedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ffmpegLoaded, setFfmpegLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ffmpegRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const switchLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[switchLocale]\": ()=>{\n            const newLocale = locale === 'en' ? 'fr' : 'en';\n            const newPath = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n            router.push(newPath);\n        }\n    }[\"VideoConverter.useCallback[switchLocale]\"], [\n        locale,\n        pathname,\n        router\n    ]);\n    const loadFFmpeg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[loadFFmpeg]\": async ()=>{\n            if (ffmpegRef.current || ffmpegLoaded) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 10\n                });\n                const ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__.FFmpeg();\n                ffmpegRef.current = ffmpeg;\n                // Load FFmpeg with progress tracking\n                ffmpeg.on('log', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { message } = param;\n                        console.log('FFmpeg log:', message);\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                ffmpeg.on('progress', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { progress } = param;\n                        if (conversion.stage === 'converting') {\n                            const adjustedProgress = 50 + progress * 40 // 50-90%\n                            ;\n                            setConversion({\n                                \"VideoConverter.useCallback[loadFFmpeg]\": (prev)=>({\n                                        ...prev,\n                                        progress: adjustedProgress\n                                    })\n                            }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                        }\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                setConversion({\n                    stage: 'loading',\n                    progress: 30\n                });\n                // Load FFmpeg core with stable version\n                const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n                await ffmpeg.load({\n                    coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n                    wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n                });\n                setFfmpegLoaded(true);\n                setConversion({\n                    stage: 'idle',\n                    progress: 0\n                });\n            } catch (error) {\n                console.error('Failed to load FFmpeg:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[loadFFmpeg]\"], [\n        ffmpegLoaded,\n        conversion.stage\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileSelect]\": (selectedFile)=>{\n            // Validate file type\n            const allowedTypes = [\n                'video/mp4',\n                'video/avi',\n                'video/mov',\n                'video/quicktime',\n                'video/x-msvideo',\n                'video/mkv',\n                'video/x-matroska',\n                'video/webm',\n                'video/3gpp',\n                'video/x-flv',\n                'video/x-ms-wmv'\n            ];\n            const allowedExtensions = [\n                '.mp4',\n                '.avi',\n                '.mov',\n                '.mkv',\n                '.webm',\n                '.3gp',\n                '.flv',\n                '.wmv',\n                '.m4v',\n                '.mpg',\n                '.mpeg',\n                '.ogv'\n            ];\n            const fileName = selectedFile.name.toLowerCase();\n            const hasValidType = allowedTypes.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidType\": (type)=>selectedFile.type.includes(type)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidType\"]);\n            const hasValidExtension = allowedExtensions.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidExtension\": (ext)=>fileName.endsWith(ext)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidExtension\"]);\n            if (!hasValidType && !hasValidExtension) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.unsupportedFormat')\n                });\n                return;\n            }\n            // Check file size (25MB limit for better memory management)\n            if (selectedFile.size > 25 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.fileTooLarge')\n                });\n                return;\n            }\n            setFile(selectedFile);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n        }\n    }[\"VideoConverter.useCallback[handleFileSelect]\"], []);\n    const convertVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[convertVideo]\": async ()=>{\n            if (!file || !ffmpegRef.current) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 20\n                });\n                const ffmpeg = ffmpegRef.current;\n                // Write input file\n                const inputFileName = \"input_\".concat(Date.now(), \".mp4\");\n                const outputFileName = \"output_\".concat(Date.now(), \".webm\");\n                await ffmpeg.writeFile(inputFileName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.fetchFile)(file));\n                setConversion({\n                    stage: 'converting',\n                    progress: 50\n                });\n                // Use the simplest possible conversion to avoid memory issues\n                await ffmpeg.exec([\n                    '-i',\n                    inputFileName,\n                    '-s',\n                    '512x512',\n                    '-b:v',\n                    '300k',\n                    '-b:a',\n                    '64k',\n                    outputFileName\n                ]);\n                setConversion({\n                    stage: 'converting',\n                    progress: 95\n                });\n                // Read output file\n                const data = await ffmpeg.readFile(outputFileName);\n                const blob = new Blob([\n                    data\n                ], {\n                    type: 'video/webm'\n                });\n                setConvertedVideo(blob);\n                setConversion({\n                    stage: 'completed',\n                    progress: 100\n                });\n                // Clean up\n                try {\n                    await ffmpeg.deleteFile(inputFileName);\n                    await ffmpeg.deleteFile(outputFileName);\n                } catch (cleanupError) {\n                    console.log('Cleanup completed');\n                }\n            } catch (error) {\n                console.error('Conversion error:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[convertVideo]\"], [\n        file,\n        t\n    ]);\n    const downloadVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[downloadVideo]\": ()=>{\n            if (!convertedVideo) return;\n            const url = URL.createObjectURL(convertedVideo);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"converted-\".concat(Date.now(), \".webm\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"VideoConverter.useCallback[downloadVideo]\"], [\n        convertedVideo\n    ]);\n    const resetConverter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[resetConverter]\": async ()=>{\n            setFile(null);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Clean up FFmpeg memory if there was an error\n            if (ffmpegRef.current && conversion.stage === 'error') {\n                try {\n                    // Try to clean up any remaining files\n                    await ffmpegRef.current.deleteFile('input.mp4').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                    await ffmpegRef.current.deleteFile('output.webm').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                } catch (error) {\n                    console.log('Cleanup completed');\n                }\n            }\n        }\n    }[\"VideoConverter.useCallback[resetConverter]\"], [\n        conversion.stage\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"VideoConverter.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"VideoConverter.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            if (droppedFiles.length > 0) {\n                handleFileSelect(droppedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleDrop]\"], [\n        handleFileSelect\n    ]);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileInputChange]\": (e)=>{\n            const selectedFiles = e.target.files;\n            if (selectedFiles && selectedFiles.length > 0) {\n                handleFileSelect(selectedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleFileInputChange]\"], [\n        handleFileSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: t('subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: switchLocale,\n                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            t('language.switch')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            !file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-area p-8 text-center cursor-pointer \".concat(isDragOver ? 'dragover' : ''),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                        children: t('upload.dragDrop')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                        children: t('upload.or')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 dark:text-blue-400 mb-4\",\n                        children: t('upload.clickToSelect')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: t('upload.maxSize')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"video/*\",\n                        onChange: handleFileInputChange,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this),\n            file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Selected File:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-500\",\n                                children: [\n                                    \"Size: \",\n                                    (file.size / (1024 * 1024)).toFixed(2),\n                                    \" MB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            conversion.stage === 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ffmpegLoaded ? convertVideo : loadFFmpeg,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this),\n                                            ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.cancel')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadVideo,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.download')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.convertAnother')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this),\n            (conversion.stage === 'loading' || conversion.stage === 'converting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    Math.round(conversion.progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-bar bg-blue-600 h-2 rounded-full\",\n                            style: {\n                                width: \"\".concat(conversion.progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 dark:text-red-200\",\n                                    children: \"Conversion Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600 dark:text-red-300 mt-1\",\n                                    children: conversion.error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetConverter,\n                                    className: \"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-3 w-3 text-white\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-green-800 dark:text-green-200\",\n                                    children: t('upload.success')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600 dark:text-green-300\",\n                                    children: t('upload.downloadReady')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoConverter, \"yK1HBG1EYjIXbihU4jvy68Slolw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = VideoConverter;\nvar _c;\n$RefreshReg$(_c, \"VideoConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoConverter.tsx\n"));

/***/ })

});