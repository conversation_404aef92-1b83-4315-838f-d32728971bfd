'use client'

import { useState, useRef, useCallback } from 'react'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { Upload, Download, Play, Pause, RotateCcw, AlertCircle, Globe } from 'lucide-react'
import { useTranslations, useLocale } from 'next-intl'
import { useRouter, usePathname } from 'next/navigation'

interface ConversionStage {
  stage: 'idle' | 'loading' | 'converting' | 'completed' | 'error'
  progress: number
  error?: string
}

export default function VideoConverter() {
  const [file, setFile] = useState<File | null>(null)
  const [conversion, setConversion] = useState<ConversionStage>({ stage: 'idle', progress: 0 })
  const [convertedVideo, setConvertedVideo] = useState<Blob | null>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [ffmpegLoaded, setFfmpegLoaded] = useState(false)

  const ffmpegRef = useRef<FFmpeg | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const t = useTranslations()
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const switchLocale = useCallback(() => {
    const newLocale = locale === 'en' ? 'fr' : 'en'
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`)
    router.push(newPath)
  }, [locale, pathname, router])

  const loadFFmpeg = useCallback(async () => {
    if (ffmpegRef.current || ffmpegLoaded) return

    try {
      setConversion({ stage: 'loading', progress: 10 })
      
      const ffmpeg = new FFmpeg()
      ffmpegRef.current = ffmpeg

      // Load FFmpeg with progress tracking
      ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message)
      })

      ffmpeg.on('progress', ({ progress }) => {
        if (conversion.stage === 'converting') {
          const adjustedProgress = 50 + progress * 40 // 50-90%
          setConversion(prev => ({ ...prev, progress: adjustedProgress }))
        }
      })

      setConversion({ stage: 'loading', progress: 30 })

      // Load FFmpeg core with stable version
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'
      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      })

      setFfmpegLoaded(true)
      setConversion({ stage: 'idle', progress: 0 })
    } catch (error) {
      console.error('Failed to load FFmpeg:', error)
      setConversion({
        stage: 'error',
        progress: 0,
        error: t('errors.conversionFailed')
      })
    }
  }, [ffmpegLoaded, conversion.stage])

  const handleFileSelect = useCallback((selectedFile: File) => {
    // Validate file type
    const allowedTypes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/quicktime',
      'video/x-msvideo', 'video/mkv', 'video/x-matroska',
      'video/webm', 'video/3gpp', 'video/x-flv', 'video/x-ms-wmv'
    ]
    
    const allowedExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.webm',
      '.3gp', '.flv', '.wmv', '.m4v', '.mpg',
      '.mpeg', '.ogv'
    ]

    const fileName = selectedFile.name.toLowerCase()
    const hasValidType = allowedTypes.some(type => selectedFile.type.includes(type))
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))

    if (!hasValidType && !hasValidExtension) {
      setConversion({
        stage: 'error',
        progress: 0,
        error: t('errors.unsupportedFormat')
      })
      return
    }

    // Check file size (25MB limit for better memory management)
    if (selectedFile.size > 25 * 1024 * 1024) {
      setConversion({
        stage: 'error',
        progress: 0,
        error: t('errors.fileTooLarge')
      })
      return
    }

    setFile(selectedFile)
    setConvertedVideo(null)
    setConversion({ stage: 'idle', progress: 0 })
  }, [])

  const convertVideo = useCallback(async () => {
    if (!file || !ffmpegRef.current) return

    // Check available memory (rough estimate)
    if (file.size > 10 * 1024 * 1024 && performance.memory && performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {
      setConversion({
        stage: 'error',
        progress: 0,
        error: 'Insufficient memory. Please try a smaller file or refresh the page.'
      })
      return
    }

    try {
      setConversion({ stage: 'loading', progress: 20 })

      const ffmpeg = ffmpegRef.current

      // Write input file
      const inputFileName = `input_${Date.now()}.mp4`
      const outputFileName = `output_${Date.now()}.webm`

      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      setConversion({ stage: 'converting', progress: 50 })

      // Use the simplest possible conversion to avoid memory issues
      await ffmpeg.exec([
        '-i', inputFileName,
        '-s', '512x512',
        '-b:v', '300k',
        '-b:a', '64k',
        outputFileName
      ])

      setConversion({ stage: 'converting', progress: 95 })

      // Read output file
      const data = await ffmpeg.readFile(outputFileName)
      const blob = new Blob([data], { type: 'video/webm' })

      setConvertedVideo(blob)
      setConversion({ stage: 'completed', progress: 100 })

      // Clean up
      try {
        await ffmpeg.deleteFile(inputFileName)
        await ffmpeg.deleteFile(outputFileName)
      } catch (cleanupError) {
        console.log('Cleanup completed')
      }

    } catch (error) {
      console.error('Conversion error:', error)
      setConversion({
        stage: 'error',
        progress: 0,
        error: t('errors.conversionFailed')
      })
    }
  }, [file, t])

  const downloadVideo = useCallback(() => {
    if (!convertedVideo) return

    const url = URL.createObjectURL(convertedVideo)
    const a = document.createElement('a')
    a.href = url
    a.download = `converted-${Date.now()}.webm`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [convertedVideo])

  const resetConverter = useCallback(async () => {
    setFile(null)
    setConvertedVideo(null)
    setConversion({ stage: 'idle', progress: 0 })
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }

    // Clean up FFmpeg memory if there was an error
    if (ffmpegRef.current && conversion.stage === 'error') {
      try {
        // Try to clean up any remaining files
        await ffmpegRef.current.deleteFile('input.mp4').catch(() => {})
        await ffmpegRef.current.deleteFile('output.webm').catch(() => {})
      } catch (error) {
        console.log('Cleanup completed')
      }
    }
  }, [conversion.stage])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles[0])
    }
  }, [handleFileSelect])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileSelect(selectedFiles[0])
    }
  }, [handleFileSelect])

  return (
    <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      {/* Header with Language Switcher */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {t('subtitle')}
          </p>
        </div>
        <button
          onClick={switchLocale}
          className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
        >
          <Globe className="h-4 w-4" />
          {t('language.switch')}
        </button>
      </div>

      {/* File Upload Area */}
      {!file && (
        <div
          className={`upload-area p-8 text-center cursor-pointer ${
            isDragOver ? 'dragover' : ''
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('upload.dragDrop')}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            {t('upload.or')}
          </p>
          <p className="text-sm text-blue-600 dark:text-blue-400 mb-4">
            {t('upload.clickToSelect')}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {t('upload.maxSize')}
          </p>
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      )}

      {/* File Info and Controls */}
      {file && (
        <div className="space-y-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Selected File:</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">{file.name}</p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Size: {(file.size / (1024 * 1024)).toFixed(2)} MB
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {conversion.stage === 'idle' && (
              <>
                <button
                  onClick={ffmpegLoaded ? convertVideo : loadFFmpeg}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  {ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'}
                </button>
                <button
                  onClick={resetConverter}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  {t('buttons.cancel')}
                </button>
              </>
            )}

            {conversion.stage === 'completed' && (
              <>
                <button
                  onClick={downloadVideo}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t('buttons.download')}
                </button>
                <button
                  onClick={resetConverter}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  {t('buttons.convertAnother')}
                </button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Progress and Status */}
      {(conversion.stage === 'loading' || conversion.stage === 'converting') && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(conversion.progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="progress-bar bg-blue-600 h-2 rounded-full"
              style={{ width: `${conversion.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Error Display */}
      {conversion.stage === 'error' && (
        <div className="mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200">Conversion Error</h3>
              <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                {conversion.error}
              </p>
              <button
                onClick={resetConverter}
                className="mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {conversion.stage === 'completed' && (
        <div className="mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="h-5 w-5 bg-green-500 rounded-full flex items-center justify-center">
              <svg className="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-green-800 dark:text-green-200">{t('upload.success')}</h3>
              <p className="text-sm text-green-600 dark:text-green-300">
                {t('upload.downloadReady')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
