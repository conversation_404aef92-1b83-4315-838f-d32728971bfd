"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VideoConverter() {\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conversion, setConversion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stage: 'idle',\n        progress: 0\n    });\n    const [convertedVideo, setConvertedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ffmpegLoaded, setFfmpegLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ffmpegRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const switchLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[switchLocale]\": ()=>{\n            const newLocale = locale === 'en' ? 'fr' : 'en';\n            const newPath = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n            router.push(newPath);\n        }\n    }[\"VideoConverter.useCallback[switchLocale]\"], [\n        locale,\n        pathname,\n        router\n    ]);\n    const loadFFmpeg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[loadFFmpeg]\": async ()=>{\n            if (ffmpegRef.current || ffmpegLoaded) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 10\n                });\n                const ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__.FFmpeg();\n                ffmpegRef.current = ffmpeg;\n                // Load FFmpeg with progress tracking\n                ffmpeg.on('log', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { message } = param;\n                        console.log('FFmpeg log:', message);\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                ffmpeg.on('progress', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { progress } = param;\n                        if (conversion.stage === 'converting') {\n                            const adjustedProgress = 50 + progress * 40 // 50-90%\n                            ;\n                            setConversion({\n                                \"VideoConverter.useCallback[loadFFmpeg]\": (prev)=>({\n                                        ...prev,\n                                        progress: adjustedProgress\n                                    })\n                            }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                        }\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                setConversion({\n                    stage: 'loading',\n                    progress: 30\n                });\n                // Load FFmpeg core with stable version\n                const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n                await ffmpeg.load({\n                    coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n                    wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n                });\n                setFfmpegLoaded(true);\n                setConversion({\n                    stage: 'idle',\n                    progress: 0\n                });\n            } catch (error) {\n                console.error('Failed to load FFmpeg:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[loadFFmpeg]\"], [\n        ffmpegLoaded,\n        conversion.stage\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileSelect]\": (selectedFile)=>{\n            // Validate file type\n            const allowedTypes = [\n                'video/mp4',\n                'video/avi',\n                'video/mov',\n                'video/quicktime',\n                'video/x-msvideo',\n                'video/mkv',\n                'video/x-matroska',\n                'video/webm',\n                'video/3gpp',\n                'video/x-flv',\n                'video/x-ms-wmv'\n            ];\n            const allowedExtensions = [\n                '.mp4',\n                '.avi',\n                '.mov',\n                '.mkv',\n                '.webm',\n                '.3gp',\n                '.flv',\n                '.wmv',\n                '.m4v',\n                '.mpg',\n                '.mpeg',\n                '.ogv'\n            ];\n            const fileName = selectedFile.name.toLowerCase();\n            const hasValidType = allowedTypes.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidType\": (type)=>selectedFile.type.includes(type)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidType\"]);\n            const hasValidExtension = allowedExtensions.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidExtension\": (ext)=>fileName.endsWith(ext)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidExtension\"]);\n            if (!hasValidType && !hasValidExtension) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.unsupportedFormat')\n                });\n                return;\n            }\n            // Check file size (50MB limit for better memory management)\n            if (selectedFile.size > 50 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.fileTooLarge')\n                });\n                return;\n            }\n            setFile(selectedFile);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n        }\n    }[\"VideoConverter.useCallback[handleFileSelect]\"], []);\n    const convertVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[convertVideo]\": async ()=>{\n            if (!file || !ffmpegRef.current) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 20\n                });\n                const ffmpeg = ffmpegRef.current;\n                // Write input file\n                await ffmpeg.writeFile('input.mp4', await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.fetchFile)(file));\n                setConversion({\n                    stage: 'converting',\n                    progress: 50\n                });\n                // Convert video with simpler, more memory-efficient settings\n                await ffmpeg.exec([\n                    '-i',\n                    'input.mp4',\n                    '-vf',\n                    'scale=512:512:force_original_aspect_ratio=decrease,pad=512:512:(ow-iw)/2:(oh-ih)/2:black',\n                    '-c:v',\n                    'libvpx',\n                    '-crf',\n                    '35',\n                    '-b:v',\n                    '500k',\n                    '-c:a',\n                    'libvorbis',\n                    '-b:a',\n                    '96k',\n                    '-f',\n                    'webm',\n                    '-threads',\n                    '1',\n                    'output.webm'\n                ]);\n                setConversion({\n                    stage: 'converting',\n                    progress: 95\n                });\n                // Read output file\n                const data = await ffmpeg.readFile('output.webm');\n                const blob = new Blob([\n                    data\n                ], {\n                    type: 'video/webm'\n                });\n                setConvertedVideo(blob);\n                setConversion({\n                    stage: 'completed',\n                    progress: 100\n                });\n                // Clean up\n                await ffmpeg.deleteFile('input.mp4');\n                await ffmpeg.deleteFile('output.webm');\n            } catch (error) {\n                console.error('Conversion error:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[convertVideo]\"], [\n        file\n    ]);\n    const downloadVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[downloadVideo]\": ()=>{\n            if (!convertedVideo) return;\n            const url = URL.createObjectURL(convertedVideo);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"converted-\".concat(Date.now(), \".webm\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"VideoConverter.useCallback[downloadVideo]\"], [\n        convertedVideo\n    ]);\n    const resetConverter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[resetConverter]\": async ()=>{\n            setFile(null);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Clean up FFmpeg memory if there was an error\n            if (ffmpegRef.current && conversion.stage === 'error') {\n                try {\n                    // Try to clean up any remaining files\n                    await ffmpegRef.current.deleteFile('input.mp4').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                    await ffmpegRef.current.deleteFile('output.webm').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                } catch (error) {\n                    console.log('Cleanup completed');\n                }\n            }\n        }\n    }[\"VideoConverter.useCallback[resetConverter]\"], [\n        conversion.stage\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"VideoConverter.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"VideoConverter.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            if (droppedFiles.length > 0) {\n                handleFileSelect(droppedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleDrop]\"], [\n        handleFileSelect\n    ]);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileInputChange]\": (e)=>{\n            const selectedFiles = e.target.files;\n            if (selectedFiles && selectedFiles.length > 0) {\n                handleFileSelect(selectedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleFileInputChange]\"], [\n        handleFileSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: t('subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: switchLocale,\n                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            t('language.switch')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            !file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-area p-8 text-center cursor-pointer \".concat(isDragOver ? 'dragover' : ''),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                        children: t('upload.dragDrop')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                        children: t('upload.or')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 dark:text-blue-400 mb-4\",\n                        children: t('upload.clickToSelect')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: t('upload.maxSize')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"video/*\",\n                        onChange: handleFileInputChange,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Selected File:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-500\",\n                                children: [\n                                    \"Size: \",\n                                    (file.size / (1024 * 1024)).toFixed(2),\n                                    \" MB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            conversion.stage === 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ffmpegLoaded ? convertVideo : loadFFmpeg,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.cancel')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadVideo,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.download')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.convertAnother')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this),\n            (conversion.stage === 'loading' || conversion.stage === 'converting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    Math.round(conversion.progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-bar bg-blue-600 h-2 rounded-full\",\n                            style: {\n                                width: \"\".concat(conversion.progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 dark:text-red-200\",\n                                    children: \"Conversion Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600 dark:text-red-300 mt-1\",\n                                    children: conversion.error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetConverter,\n                                    className: \"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-3 w-3 text-white\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-green-800 dark:text-green-200\",\n                                    children: t('upload.success')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600 dark:text-green-300\",\n                                    children: t('upload.downloadReady')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoConverter, \"yK1HBG1EYjIXbihU4jvy68Slolw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = VideoConverter;\nvar _c;\n$RefreshReg$(_c, \"VideoConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoConverter.tsx\n"));

/***/ })

});