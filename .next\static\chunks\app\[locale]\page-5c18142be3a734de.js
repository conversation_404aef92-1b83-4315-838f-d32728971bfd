(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{835:(e,r,t)=>{Promise.resolve().then(t.bind(t,9295))},9295:(e,r,t)=>{"use strict";t.d(r,{default:()=>p});var s=t(5155),a=t(2115),l=t(5654),o=t(4148),n=t(4869),i=t(9869),d=t(5690),c=t(133),g=t(1788),m=t(5339),x=t(7043),u=t(5695);function p(){let[e,r]=(0,a.useState)(null),[t,p]=(0,a.useState)({stage:"idle",progress:0}),[h,b]=(0,a.useState)(null),[v,f]=(0,a.useState)(!1),[j,y]=(0,a.useState)(!1),w=(0,a.useRef)(null),k=(0,a.useRef)(null),N=(0,x.useTranslations)(),C=(0,x.useLocale)(),F=(0,u.useRouter)(),A=(0,u.usePathname)(),L=(0,a.useCallback)(()=>{let e="en"===C?"fr":"en",r=A.replace("/".concat(C),"/".concat(e));F.push(r)},[C,A,F]),R=(0,a.useCallback)(async()=>{if(!w.current&&!j)try{p({stage:"loading",progress:10});let e=new l.m;w.current=e,e.on("log",e=>{let{message:r}=e;console.log("FFmpeg log:",r)}),e.on("progress",e=>{let{progress:r}=e;if("converting"===t.stage){let e=50+40*r;p(r=>({...r,progress:e}))}}),p({stage:"loading",progress:30});let r="https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd";await e.load({coreURL:await (0,o.KA)("".concat(r,"/ffmpeg-core.js"),"text/javascript"),wasmURL:await (0,o.KA)("".concat(r,"/ffmpeg-core.wasm"),"application/wasm")}),y(!0),p({stage:"idle",progress:0})}catch(e){console.error("Failed to load FFmpeg:",e),p({stage:"error",progress:0,error:N("errors.conversionFailed")})}},[j,t.stage]),S=(0,a.useCallback)(e=>{let t=e.name.toLowerCase(),s=["video/mp4","video/avi","video/mov","video/quicktime","video/x-msvideo","video/mkv","video/x-matroska","video/webm","video/3gpp","video/x-flv","video/x-ms-wmv"].some(r=>e.type.includes(r)),a=[".mp4",".avi",".mov",".mkv",".webm",".3gp",".flv",".wmv",".m4v",".mpg",".mpeg",".ogv"].some(e=>t.endsWith(e));return s||a?e.size>0x6400000?void p({stage:"error",progress:0,error:N("errors.fileTooLarge")}):void(r(e),b(null),p({stage:"idle",progress:0})):void p({stage:"error",progress:0,error:N("errors.unsupportedFormat")})},[]),_=(0,a.useCallback)(async()=>{if(e&&w.current)try{p({stage:"loading",progress:20});let r=w.current;await r.writeFile("input.mp4",await (0,o.t2)(e)),p({stage:"converting",progress:50}),await r.exec(["-i","input.mp4","-vf","scale=512:512:force_original_aspect_ratio=decrease,pad=512:512:(ow-iw)/2:(oh-ih)/2:black","-c:v","libvpx-vp9","-crf","30","-b:v","0","-b:a","128k","-c:a","libopus","-f","webm","output.webm"]),p({stage:"converting",progress:95});let t=await r.readFile("output.webm"),s=new Blob([t],{type:"video/webm"});b(s),p({stage:"completed",progress:100}),await r.deleteFile("input.mp4"),await r.deleteFile("output.webm")}catch(e){console.error("Conversion error:",e),p({stage:"error",progress:0,error:N("errors.conversionFailed")})}},[e]),D=(0,a.useCallback)(()=>{if(!h)return;let e=URL.createObjectURL(h),r=document.createElement("a");r.href=e,r.download="converted-".concat(Date.now(),".webm"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(e)},[h]),U=(0,a.useCallback)(()=>{r(null),b(null),p({stage:"idle",progress:0}),k.current&&(k.current.value="")},[]),z=(0,a.useCallback)(e=>{e.preventDefault(),f(!0)},[]),E=(0,a.useCallback)(e=>{e.preventDefault(),f(!1)},[]),O=(0,a.useCallback)(e=>{e.preventDefault(),f(!1);let r=Array.from(e.dataTransfer.files);r.length>0&&S(r[0])},[S]),T=(0,a.useCallback)(e=>{let r=e.target.files;r&&r.length>0&&S(r[0])},[S]);return(0,s.jsxs)("div",{className:"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 dark:text-white mb-2",children:N("title")}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:N("subtitle")})]}),(0,s.jsxs)("button",{onClick:L,className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),N("language.switch")]})]}),!e&&(0,s.jsxs)("div",{className:"upload-area p-8 text-center cursor-pointer ".concat(v?"dragover":""),onDragOver:z,onDragLeave:E,onDrop:O,onClick:()=>{var e;return null==(e=k.current)?void 0:e.click()},children:[(0,s.jsx)(i.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2",children:N("upload.dragDrop")}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-2",children:N("upload.or")}),(0,s.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400 mb-4",children:N("upload.clickToSelect")}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:N("upload.maxSize")}),(0,s.jsx)("input",{ref:k,type:"file",accept:"video/*",onChange:T,className:"hidden"})]}),e&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Selected File:"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:["Size: ",(e.size/1048576).toFixed(2)," MB"]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:["idle"===t.stage&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:j?_:R,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),j?N("buttons.selectFile"):"Load Converter"]}),(0,s.jsxs)("button",{onClick:U,className:"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),N("buttons.cancel")]})]}),"completed"===t.stage&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:D,className:"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),N("buttons.download")]}),(0,s.jsxs)("button",{onClick:U,className:"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),N("buttons.convertAnother")]})]})]})]}),("loading"===t.stage||"converting"===t.stage)&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"loading"===t.stage?N("progress.preparing"):N("upload.processing")}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(t.progress),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"progress-bar bg-blue-600 h-2 rounded-full",style:{width:"".concat(t.progress,"%")}})})]}),"error"===t.stage&&(0,s.jsx)("div",{className:"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-red-800 dark:text-red-200",children:"Conversion Error"}),(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-300 mt-1",children:t.error}),(0,s.jsx)("button",{onClick:U,className:"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors",children:"Try Again"})]})]})}),"completed"===t.stage&&(0,s.jsx)("div",{className:"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"h-3 w-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-green-800 dark:text-green-200",children:N("upload.success")}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-300",children:N("upload.downloadReady")})]})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[699,441,684,358],()=>r(835)),_N_E=e.O()}]);