"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"(app-pages-browser)/./node_modules/@ffmpeg/ffmpeg/dist/esm/index.js\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ffmpeg/util */ \"(app-pages-browser)/./node_modules/@ffmpeg/util/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VideoConverter() {\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conversion, setConversion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stage: 'idle',\n        progress: 0\n    });\n    const [convertedVideo, setConvertedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ffmpegLoaded, setFfmpegLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ffmpegRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const switchLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[switchLocale]\": ()=>{\n            const newLocale = locale === 'en' ? 'fr' : 'en';\n            const newPath = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n            router.push(newPath);\n        }\n    }[\"VideoConverter.useCallback[switchLocale]\"], [\n        locale,\n        pathname,\n        router\n    ]);\n    const loadFFmpeg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[loadFFmpeg]\": async ()=>{\n            if (ffmpegRef.current || ffmpegLoaded) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 10\n                });\n                const ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__.FFmpeg();\n                ffmpegRef.current = ffmpeg;\n                // Load FFmpeg with progress tracking\n                ffmpeg.on('log', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { message } = param;\n                        console.log('FFmpeg log:', message);\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                ffmpeg.on('progress', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": (param)=>{\n                        let { progress } = param;\n                        if (conversion.stage === 'converting') {\n                            const adjustedProgress = 50 + progress * 40 // 50-90%\n                            ;\n                            setConversion({\n                                \"VideoConverter.useCallback[loadFFmpeg]\": (prev)=>({\n                                        ...prev,\n                                        progress: adjustedProgress\n                                    })\n                            }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                        }\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                setConversion({\n                    stage: 'loading',\n                    progress: 30\n                });\n                // Load FFmpeg core with stable version\n                const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n                await ffmpeg.load({\n                    coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.js\"), 'text/javascript'),\n                    wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(\"\".concat(baseURL, \"/ffmpeg-core.wasm\"), 'application/wasm')\n                });\n                setFfmpegLoaded(true);\n                setConversion({\n                    stage: 'idle',\n                    progress: 0\n                });\n            } catch (error) {\n                console.error('Failed to load FFmpeg:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[loadFFmpeg]\"], [\n        ffmpegLoaded,\n        conversion.stage\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileSelect]\": (selectedFile)=>{\n            // Validate file type\n            const allowedTypes = [\n                'video/mp4',\n                'video/avi',\n                'video/mov',\n                'video/quicktime',\n                'video/x-msvideo',\n                'video/mkv',\n                'video/x-matroska',\n                'video/webm',\n                'video/3gpp',\n                'video/x-flv',\n                'video/x-ms-wmv'\n            ];\n            const allowedExtensions = [\n                '.mp4',\n                '.avi',\n                '.mov',\n                '.mkv',\n                '.webm',\n                '.3gp',\n                '.flv',\n                '.wmv',\n                '.m4v',\n                '.mpg',\n                '.mpeg',\n                '.ogv'\n            ];\n            const fileName = selectedFile.name.toLowerCase();\n            const hasValidType = allowedTypes.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidType\": (type)=>selectedFile.type.includes(type)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidType\"]);\n            const hasValidExtension = allowedExtensions.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidExtension\": (ext)=>fileName.endsWith(ext)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidExtension\"]);\n            if (!hasValidType && !hasValidExtension) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.unsupportedFormat')\n                });\n                return;\n            }\n            // Check file size (25MB limit for better memory management)\n            if (selectedFile.size > 25 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.fileTooLarge')\n                });\n                return;\n            }\n            setFile(selectedFile);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n        }\n    }[\"VideoConverter.useCallback[handleFileSelect]\"], []);\n    const convertVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[convertVideo]\": async ()=>{\n            if (!file || !ffmpegRef.current) return;\n            // Check available memory (rough estimate)\n            if (file.size > 10 * 1024 * 1024 && performance.memory && performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: 'Insufficient memory. Please try a smaller file or refresh the page.'\n                });\n                return;\n            }\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 20\n                });\n                const ffmpeg = ffmpegRef.current;\n                // Write input file\n                const inputFileName = \"input_\".concat(Date.now(), \".mp4\");\n                const outputFileName = \"output_\".concat(Date.now(), \".webm\");\n                await ffmpeg.writeFile(inputFileName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.fetchFile)(file));\n                setConversion({\n                    stage: 'converting',\n                    progress: 50\n                });\n                // Use the simplest possible conversion to avoid memory issues\n                await ffmpeg.exec([\n                    '-i',\n                    inputFileName,\n                    '-s',\n                    '512x512',\n                    '-b:v',\n                    '300k',\n                    '-b:a',\n                    '64k',\n                    outputFileName\n                ]);\n                setConversion({\n                    stage: 'converting',\n                    progress: 95\n                });\n                // Read output file\n                const data = await ffmpeg.readFile(outputFileName);\n                const blob = new Blob([\n                    data\n                ], {\n                    type: 'video/webm'\n                });\n                setConvertedVideo(blob);\n                setConversion({\n                    stage: 'completed',\n                    progress: 100\n                });\n                // Clean up\n                try {\n                    await ffmpeg.deleteFile(inputFileName);\n                    await ffmpeg.deleteFile(outputFileName);\n                } catch (cleanupError) {\n                    console.log('Cleanup completed');\n                }\n            } catch (error) {\n                console.error('Conversion error:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[convertVideo]\"], [\n        file,\n        t\n    ]);\n    const downloadVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[downloadVideo]\": ()=>{\n            if (!convertedVideo) return;\n            const url = URL.createObjectURL(convertedVideo);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"converted-\".concat(Date.now(), \".webm\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"VideoConverter.useCallback[downloadVideo]\"], [\n        convertedVideo\n    ]);\n    const resetConverter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[resetConverter]\": async ()=>{\n            setFile(null);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Clean up FFmpeg memory if there was an error\n            if (ffmpegRef.current && conversion.stage === 'error') {\n                try {\n                    // Try to clean up any remaining files\n                    await ffmpegRef.current.deleteFile('input.mp4').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                    await ffmpegRef.current.deleteFile('output.webm').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                } catch (error) {\n                    console.log('Cleanup completed');\n                }\n            }\n        }\n    }[\"VideoConverter.useCallback[resetConverter]\"], [\n        conversion.stage\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"VideoConverter.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"VideoConverter.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            if (droppedFiles.length > 0) {\n                handleFileSelect(droppedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleDrop]\"], [\n        handleFileSelect\n    ]);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileInputChange]\": (e)=>{\n            const selectedFiles = e.target.files;\n            if (selectedFiles && selectedFiles.length > 0) {\n                handleFileSelect(selectedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleFileInputChange]\"], [\n        handleFileSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: t('subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: switchLocale,\n                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            t('language.switch')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            !file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-area p-8 text-center cursor-pointer \".concat(isDragOver ? 'dragover' : ''),\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                        children: t('upload.dragDrop')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                        children: t('upload.or')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 dark:text-blue-400 mb-4\",\n                        children: t('upload.clickToSelect')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: t('upload.maxSize')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"video/*\",\n                        onChange: handleFileInputChange,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this),\n            file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Selected File:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-500\",\n                                children: [\n                                    \"Size: \",\n                                    (file.size / (1024 * 1024)).toFixed(2),\n                                    \" MB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            conversion.stage === 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ffmpegLoaded ? convertVideo : loadFFmpeg,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this),\n                                            ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.cancel')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadVideo,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.download')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.convertAnother')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this),\n            (conversion.stage === 'loading' || conversion.stage === 'converting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    Math.round(conversion.progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-bar bg-blue-600 h-2 rounded-full\",\n                            style: {\n                                width: \"\".concat(conversion.progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 dark:text-red-200\",\n                                    children: \"Conversion Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600 dark:text-red-300 mt-1\",\n                                    children: conversion.error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetConverter,\n                                    className: \"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 374,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-3 w-3 text-white\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-green-800 dark:text-green-200\",\n                                    children: t('upload.success')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600 dark:text-green-300\",\n                                    children: t('upload.downloadReady')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoConverter, \"yK1HBG1EYjIXbihU4jvy68Slolw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = VideoConverter;\nvar _c;\n$RefreshReg$(_c, \"VideoConverter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoConverter.tsx\n"));

/***/ })

});