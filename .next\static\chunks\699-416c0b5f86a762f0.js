"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[699],{67:(e,t,r)=>{e.exports=r(1787)},133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},600:(e,t,r)=>{r.r(t),r.d(t,{ErrorCode:()=>h,FormatError:()=>eo,IntlMessageFormat:()=>ec,InvalidValueError:()=>ei,InvalidValueTypeError:()=>ea,MissingValueError:()=>es,PART_TYPE:()=>l,default:()=>ef,formatToParts:()=>el,isFormatXMLElementFn:()=>eh});var n,o,i,a,s,h,l,u=function(e,t){return(u=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}u(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function p(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create;var E=("function"==typeof SuppressedError&&SuppressedError,r(6690));function m(e){return e.type===o.literal}function d(e){return e.type===o.number}function g(e){return e.type===o.date}function y(e){return e.type===o.time}function b(e){return e.type===o.select}function v(e){return e.type===o.plural}function T(e){return e.type===o.tag}function _(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var I=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,H=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,L=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,B=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,N=/^(@+)?(\+|#+)?[rs]?$/g,S=/(\*)(0+)|(#+)(0+)|(0+)/g,R=/^(0+)$/;function M(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(N,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function P(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function O(e){var t=P(e);return t||{}}var C={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},w=new RegExp("^".concat(I.source,"*")),F=new RegExp("".concat(I.source,"*$"));function D(e,t){return{start:e,end:t}}var U=!!String.prototype.startsWith&&"_a".startsWith("a",1),G=!!String.fromCodePoint,k=!!Object.fromEntries,j=!!String.prototype.codePointAt,V=!!String.prototype.trimStart,x=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},K=!0;try{K=(null==(a=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){K=!1}var Z=U?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},W=G?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},Y=k?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},z=j?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},q=V?function(e){return e.trimStart()}:function(e){return e.replace(w,"")},Q=x?function(e){return e.trimEnd()}:function(e){return e.replace(F,"")};function J(e,t){return new RegExp(e,t)}if(K){var $=J("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return $.lastIndex=t,null!=(r=$.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=z(e,t);if(void 0===o||er(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return W.apply(void 0,r)};var ee=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var h=this.clonePosition();this.bump(),i.push({type:o.pound,location:D(h,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&et(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,D(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:D(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,D(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,h=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,D(r,this.clonePosition()));if(this.isEOF()||!et(this.char()))return this.error(n.INVALID_TAG,D(h,this.clonePosition()));var l=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,D(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:D(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,D(h,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var h=D(r,this.clonePosition());return{val:{type:o.literal,value:n,location:h},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(et(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return W.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),W(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,D(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:D(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,D(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:D(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,h=this.clonePosition(),l=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(l){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,D(h,u));case"number":case"date":case"time":this.bumpSpace();var c=null;if(this.bumpIf(",")){this.bumpSpace();var p=this.clonePosition(),E=this.parseSimpleArgStyleIfPossible();if(E.err)return E;var m=Q(E.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,D(this.clonePosition(),this.clonePosition()));c={style:m,styleLocation:D(p,this.clonePosition())}}var d=this.tryParseArgumentClose(a);if(d.err)return d;var g=D(a,this.clonePosition());if(c&&Z(null==c?void 0:c.style,"::",0)){var y=q(c.style.slice(2));if("number"===l){var E=this.parseNumberSkeletonFromString(y,c.styleLocation);if(E.err)return E;return{val:{type:o.number,value:r,location:g,style:E.val},err:null}}if(0===y.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var b,v=y;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),h=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(C[t||""]||C[n||""]||C["".concat(n,"-001")]||C["001"])[0]}(t);for(("H"==h||"k"==h)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=h+r}else"J"===o?r+="H":r+=o}return r}(y,this.locale));var m={type:i.dateTime,pattern:v,location:c.styleLocation,parsedOptions:this.shouldParseSkeletons?(b={},v.replace(H,function(e){var t=e.length;switch(e[0]){case"G":b.era=4===t?"long":5===t?"narrow":"short";break;case"y":b.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":b.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":b.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":b.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"a":b.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":b.hourCycle="h12",b.hour=["numeric","2-digit"][t-1];break;case"H":b.hourCycle="h23",b.hour=["numeric","2-digit"][t-1];break;case"K":b.hourCycle="h11",b.hour=["numeric","2-digit"][t-1];break;case"k":b.hourCycle="h24",b.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":b.minute=["numeric","2-digit"][t-1];break;case"s":b.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":b.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),b):{}};return{val:{type:"date"===l?o.date:o.time,value:r,location:g,style:m},err:null}}return{val:{type:"number"===l?o.number:"date"===l?o.date:o.time,value:r,location:g,style:null!=(s=null==c?void 0:c.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var T=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,D(T,f({},T)));this.bumpSpace();var _=this.parseIdentifierIfPossible(),A=0;if("select"!==l&&"offset"===_.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,D(this.clonePosition(),this.clonePosition()));this.bumpSpace();var E=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(E.err)return E;this.bumpSpace(),_=this.parseIdentifierIfPossible(),A=E.val}var I=this.tryParsePluralOrSelectOptions(e,l,t,_);if(I.err)return I;var d=this.tryParseArgumentClose(a);if(d.err)return d;var L=D(a,this.clonePosition());if("select"===l)return{val:{type:o.select,value:r,options:Y(I.val),location:L},err:null};return{val:{type:o.plural,value:r,options:Y(I.val),offset:A,pluralType:"plural"===l?"cardinal":"ordinal",location:L},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,D(h,u))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,D(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,D(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(L).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(S,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(R.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(B.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(B,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),M(o)));continue}if(N.test(n.stem)){t=f(f({},t),M(n.stem));continue}var i=P(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!R.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],h=new Set,l=o.value,u=o.location;;){if(0===l.length){var c=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;u=D(c,this.clonePosition()),l=this.message.slice(c.offset,this.offset())}else break}if(h.has(l))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,u);"other"===l&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,D(this.clonePosition(),this.clonePosition()));var E=this.parseMessage(e+1,t,r);if(E.err)return E;var m=this.tryParseArgumentClose(p);if(m.err)return m;s.push([l,{value:E.val,location:D(p,this.clonePosition())}]),h.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,u=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,D(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,D(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=D(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=z(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&er(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function et(e){return e>=97&&e<=122||e>=65&&e<=90}function er(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function en(e,t){void 0===t&&(t={});var r=new ee(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,b(t)||v(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else d(t)&&_(t.style)||(g(t)||y(t))&&A(t.style)?delete t.style.location:T(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(h||(h={}));var eo=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return c(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ei=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),h.INVALID_VALUE,o)||this}return c(t,e),t}(eo),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),h.INVALID_VALUE,n)||this}return c(t,e),t}(eo),es=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),h.MISSING_VALUE,r)||this}return c(t,e),t}(eo);function eh(e){return"function"==typeof e}function el(e,t,r,n,i,a,s){if(1===e.length&&m(e[0]))return[{type:l.literal,value:e[0].value}];for(var u=[],c=0;c<e.length;c++){var f=e[c];if(m(f)){u.push({type:l.literal,value:f.value});continue}if(f.type===o.pound){"number"==typeof a&&u.push({type:l.literal,value:r.getNumberFormat(t).format(a)});continue}var p=f.value;if(!(i&&p in i))throw new es(p,s);var E=i[p];if(f.type===o.argument){E&&"string"!=typeof E&&"number"!=typeof E||(E="string"==typeof E||"number"==typeof E?String(E):""),u.push({type:"string"==typeof E?l.literal:l.object,value:E});continue}if(g(f)){var I="string"==typeof f.style?n.date[f.style]:A(f.style)?f.style.parsedOptions:void 0;u.push({type:l.literal,value:r.getDateTimeFormat(t,I).format(E)});continue}if(y(f)){var I="string"==typeof f.style?n.time[f.style]:A(f.style)?f.style.parsedOptions:n.time.medium;u.push({type:l.literal,value:r.getDateTimeFormat(t,I).format(E)});continue}if(d(f)){var I="string"==typeof f.style?n.number[f.style]:_(f.style)?f.style.parsedOptions:void 0;I&&I.scale&&(E*=I.scale||1),u.push({type:l.literal,value:r.getNumberFormat(t,I).format(E)});continue}if(T(f)){var H=f.children,L=f.value,B=i[L];if(!eh(B))throw new ea(L,"function",s);var N=B(el(H,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(N)||(N=[N]),u.push.apply(u,N.map(function(e){return{type:"string"==typeof e?l.literal:l.object,value:e}}))}if(b(f)){var S=f.options[E]||f.options.other;if(!S)throw new ei(f.value,E,Object.keys(f.options),s);u.push.apply(u,el(S.value,t,r,n,i));continue}if(v(f)){var S=f.options["=".concat(E)];if(!S){if(!Intl.PluralRules)throw new eo('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',h.MISSING_INTL_API,s);var R=r.getPluralRules(t,{type:f.pluralType}).select(E-(f.offset||0));S=f.options[R]||f.options.other}if(!S)throw new ei(f.value,E,Object.keys(f.options),s);u.push.apply(u,el(S.value,t,r,n,i,E-(f.offset||0)));continue}}return u.length<2?u:u.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===l.literal&&t.type===l.literal?r.value+=t.value:e.push(t),e},[])}function eu(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(l||(l={}));var ec=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===l.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return el(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=s.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=o||{},u=(h.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(h,["formatters"]));this.ast=e.__parse(t,f(f({},u),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,E.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eu(a.number),strategy:E.strategies.variadic}),getDateTimeFormat:(0,E.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eu(a.dateTime),strategy:E.strategies.variadic}),getPluralRules:(0,E.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,p([void 0],t,!1)))},{cache:eu(a.pluralRules),strategy:E.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=en,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=ec},942:(e,t,r)=>{e.exports=r(7526)},1684:(e,t,r)=>{t.IntlContext=r(2115).createContext(void 0)},1787:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(2115),o=r(9035),i=r(1684);r(6690);var a=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:s,getMessageFallback:h,locale:l,messages:u,now:c,onError:f,timeZone:p}=e,E=n.useMemo(()=>o.createCache(),[l]),m=n.useMemo(()=>o.createIntlFormatters(E),[E]),d=n.useMemo(()=>({...o.initializeConfig({locale:l,defaultTranslationValues:r,formats:s,getMessageFallback:h,messages:u,now:c,onError:f,timeZone:p}),formatters:m,cache:E}),[E,r,s,m,h,l,u,c,f,p]);return a.default.createElement(i.IntlContext.Provider,{value:d},t)}},1788:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2648:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(6521),o=r(7903),i=r(9035),a=r(1787),s=r(7735),h=r(7999);r(600),r(2115),r(6690),r(1684),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=o.createTranslator,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.IntlProvider=a.IntlProvider,t.useFormatter=s.useFormatter,t.useMessages=s.useMessages,t.useNow=s.useNow,t.useTimeZone=s.useTimeZone,t.useTranslations=s.useTranslations,t.useLocale=h.useLocale},4148:(e,t,r)=>{r.d(t,{t2:()=>a,KA:()=>h});let n=Error("failed to get response body reader"),o=Error("failed to complete download"),i=e=>new Promise((t,r)=>{let n=new FileReader;n.onload=()=>{let{result:e}=n;e instanceof ArrayBuffer?t(new Uint8Array(e)):t(new Uint8Array)},n.onerror=e=>{r(Error(`File could not be read! Code=${e?.target?.error?.code||-1}`))},n.readAsArrayBuffer(e)}),a=async e=>{let t;if("string"==typeof e)t=/data:_data\/([a-zA-Z]*);base64,([^"]*)/.test(e)?atob(e.split(",")[1]).split("").map(e=>e.charCodeAt(0)):await (await fetch(e)).arrayBuffer();else if(e instanceof URL)t=await (await fetch(e)).arrayBuffer();else{if(!(e instanceof File||e instanceof Blob))return new Uint8Array;t=await i(e)}return new Uint8Array(t)},s=async(e,t)=>{let r,i=await fetch(e);try{let a=parseInt(i.headers.get("Content-Length")||"-1"),s=i.body?.getReader();if(!s)throw n;let h=[],l=0;for(;;){let{done:r,value:n}=await s.read(),i=n?n.length:0;if(r){if(-1!=a&&a!==l)throw o;t&&t({url:e,total:a,received:l,delta:i,done:r});break}h.push(n),l+=i,t&&t({url:e,total:a,received:l,delta:i,done:r})}let u=new Uint8Array(l),c=0;for(let e of h)u.set(e,c),c+=e.length;r=u.buffer}catch(n){console.log("failed to send download progress event: ",n),r=await i.arrayBuffer(),t&&t({url:e,total:r.byteLength,received:r.byteLength,delta:0,done:!0})}return r},h=async(e,t,r=!1,n)=>{let o=new Blob([r?await s(e,n):await (await fetch(e)).arrayBuffer()],{type:t});return URL.createObjectURL(o)}},4869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},4946:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(7450),o=r(2115),i=r(67),a=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return a.default.createElement(i.IntlProvider,n.extends({locale:t},r))}},5339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5403:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},5654:(e,t,r)=>{var n,o;r.d(t,{m:()=>h}),function(e){e.LOAD="LOAD",e.EXEC="EXEC",e.FFPROBE="FFPROBE",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT"}(n||(n={}));let i=(()=>{let e=0;return()=>e++})();Error("unknown message type");let a=Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),s=Error("called FFmpeg.terminate()");Error("failed to import ffmpeg-core.js");class h{#e=null;#t={};#r={};#n=[];#o=[];loaded=!1;#i=()=>{this.#e&&(this.#e.onmessage=({data:{id:e,type:t,data:r}})=>{switch(t){case n.LOAD:this.loaded=!0,this.#t[e](r);break;case n.MOUNT:case n.UNMOUNT:case n.EXEC:case n.FFPROBE:case n.WRITE_FILE:case n.READ_FILE:case n.DELETE_FILE:case n.RENAME:case n.CREATE_DIR:case n.LIST_DIR:case n.DELETE_DIR:this.#t[e](r);break;case n.LOG:this.#n.forEach(e=>e(r));break;case n.PROGRESS:this.#o.forEach(e=>e(r));break;case n.ERROR:this.#r[e](r)}delete this.#t[e],delete this.#r[e]})};#a=({type:e,data:t},r=[],n)=>this.#e?new Promise((o,a)=>{let s=i();this.#e&&this.#e.postMessage({id:s,type:e,data:t},r),this.#t[s]=o,this.#r[s]=a,n?.addEventListener("abort",()=>{a(new DOMException(`Message # ${s} was aborted`,"AbortError"))},{once:!0})}):Promise.reject(a);on(e,t){"log"===e?this.#n.push(t):"progress"===e&&this.#o.push(t)}off(e,t){"log"===e?this.#n=this.#n.filter(e=>e!==t):"progress"===e&&(this.#o=this.#o.filter(e=>e!==t))}load=({classWorkerURL:e,...t}={},{signal:o}={})=>(this.#e||(this.#e=e?new Worker(new URL(e,"file:///C:/Users/<USER>/Desktop/Billie/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js"),{type:"module"}):new Worker(r.tu(new URL(r.p+r.u(361),r.b)),{type:void 0}),this.#i()),this.#a({type:n.LOAD,data:t},void 0,o));exec=(e,t=-1,{signal:r}={})=>this.#a({type:n.EXEC,data:{args:e,timeout:t}},void 0,r);ffprobe=(e,t=-1,{signal:r}={})=>this.#a({type:n.FFPROBE,data:{args:e,timeout:t}},void 0,r);terminate=()=>{for(let e of Object.keys(this.#r))this.#r[e](s),delete this.#r[e],delete this.#t[e];this.#e&&(this.#e.terminate(),this.#e=null,this.loaded=!1)};writeFile=(e,t,{signal:r}={})=>{let o=[];return t instanceof Uint8Array&&o.push(t.buffer),this.#a({type:n.WRITE_FILE,data:{path:e,data:t}},o,r)};mount=(e,t,r)=>this.#a({type:n.MOUNT,data:{fsType:e,options:t,mountPoint:r}},[]);unmount=e=>this.#a({type:n.UNMOUNT,data:{mountPoint:e}},[]);readFile=(e,t="binary",{signal:r}={})=>this.#a({type:n.READ_FILE,data:{path:e,encoding:t}},void 0,r);deleteFile=(e,{signal:t}={})=>this.#a({type:n.DELETE_FILE,data:{path:e}},void 0,t);rename=(e,t,{signal:r}={})=>this.#a({type:n.RENAME,data:{oldPath:e,newPath:t}},void 0,r);createDir=(e,{signal:t}={})=>this.#a({type:n.CREATE_DIR,data:{path:e}},void 0,t);listDir=(e,{signal:t}={})=>this.#a({type:n.LIST_DIR,data:{path:e}},void 0,t);deleteDir=(e,{signal:t}={})=>this.#a({type:n.DELETE_DIR,data:{path:e}},void 0,t)}!function(e){e.MEMFS="MEMFS",e.NODEFS="NODEFS",e.NODERAWFS="NODERAWFS",e.IDBFS="IDBFS",e.WORKERFS="WORKERFS",e.PROXYFS="PROXYFS"}(o||(o={}))},5690:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5695:(e,t,r)=>{r.r(t);var n=r(8999),o={};for(let e in n)"default"!==e&&(o[e]=()=>n[e]);r.d(t,o)},6521:(e,t,r)=>{var n=r(600),o=r(2115),i=r(9035),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let h=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class l extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function u(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function c(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,p={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=l,t.IntlErrorCode=h,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?c(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new l(h.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:p,messagesOrError:E,namespace:m,onError:d,timeZone:g}=e,y=E instanceof l;function b(e,t,r){let n=new l(t,r);return d(n),f({error:n,key:e,namespace:m})}function v(e,l,d){let v,T;if(y)return f({error:E,key:e,namespace:m});try{v=c(p,E,e,m)}catch(t){return b(e,h.MISSING_MESSAGE,t.message)}if("object"==typeof v){let t;return b(e,Array.isArray(v)?h.INVALID_MESSAGE:h.INSUFFICIENT_PATH,t)}let _=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(v,l);if(_)return _;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{T=s.getMessageFormat(v,p,function(e,t){let r=t?{...e,dateTime:u(e.dateTime,t)}:e,n=a.default.formats.date,o=t?u(n,t):n,i=a.default.formats.time,s=t?u(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...d},g),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:g,...t})}})}catch(t){return b(e,h.INVALID_MESSAGE,t.message)}try{let e=T.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...l}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return b(e,h.FORMATTING_ERROR,t.message)}}function T(e,t,r){let n=v(e,t,r);return"string"!=typeof n?b(e,h.INVALID_MESSAGE,void 0):n}return T.rich=v,T.markup=(e,t,r)=>{let n=v(e,t,r);if("string"!=typeof n){let t=new l(h.FORMATTING_ERROR,void 0);return d(t),f({error:t,key:e,namespace:m})}return n},T.raw=e=>{if(y)return f({error:E,key:e,namespace:m});try{return c(p,E,e,m)}catch(t){return b(e,h.MISSING_MESSAGE,t.message)}},T.has=e=>{if(y)return!1;try{return c(p,E,e,m),!0}catch(e){return!1}},T}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:u}=e;function c(e){var t;return null!=(t=e)&&t.timeZone||(u?e={...e,timeZone:u}:s(new l(h.ENVIRONMENT_FALLBACK,void 0))),e}function E(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new l(h.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new l(h.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return E(t,null==n?void 0:n.dateTime,t=>(t=c(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function d(){return a||(s(new l(h.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return E(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):d(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=d());let h=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(h)),s.numeric="second"===a?"auto":"always";let l=(n=a,Math.round(h/p[n]));return r.getRelativeTimeFormat(o,s).format(l,a)}catch(t){return s(new l(h.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return E(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return E(i,null==n?void 0:n.dateTime,n=>(n=c(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},6690:(e,t,r)=>{function n(e,t){var r=t&&t.cache?t.cache:h,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>l});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),h={create:function(){return new s}},l={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},7043:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(7766),o=r(7923),i=r(4946),a=r(7488);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=o.default,t.NextIntlClientProvider=i.default,Object.keys(a).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}})})},7450:(e,t)=>{function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}Object.defineProperty(t,"__esModule",{value:!0}),t.extends=r},7488:(e,t,r)=>{e.exports=r(2648)},7526:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(7999);r(2115),r(1684),t.useLocale=n.useLocale},7735:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(1787),o=r(7999),i=r(2115),a=r(6521);r(9035),r(6690),r(1684),r(600);let s=!1,h="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=o.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:s,timeZone:h}=o.useIntlContext();return i.useMemo(()=>a.createFormatter({formats:e,locale:r,now:n,onError:s,timeZone:h,_formatters:t}),[e,t,n,r,s,h])},t.useMessages=function(){let e=o.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=o.useIntlContext(),[n,a]=i.useState(r||new Date);return i.useEffect(()=>{if(!t)return;let e=setInterval(()=>{a(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return o.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:l,formats:u,formatters:c,getMessageFallback:f,locale:p,onError:E,timeZone:m}=o.useIntlContext(),d=e["!"],g=a.resolveNamespace(t,"!");return m||s||!h||(s=!0,E(new a.IntlError(a.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),i.useMemo(()=>a.createBaseTranslator({cache:n,formatters:c,getMessageFallback:f,messages:d,defaultTranslationValues:l,namespace:g,onError:E,formats:u,locale:p,timeZone:m}),[n,c,f,d,l,g,E,u,p,m])}({"!":o.useIntlContext().messages},e?"!.".concat(e):"!","!")}},7766:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(7488);function o(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let i=o(0,n.useTranslations);t.useFormatter=o(0,n.useFormatter),t.useTranslations=i,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},7903:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(6521),o=r(9035);r(600),r(2115),r(6690),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.createTranslator=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:h=o.defaultOnError,...l}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...l,onError:h,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},"!")}},7923:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(5695),o=r(942),i=r(5403);t.default=function(){let e,t=n.useParams();try{e=o.useLocale()}catch(r){if("string"!=typeof(null==t?void 0:t[i.LOCALE_SEGMENT_NAME]))throw r;e=t[i.LOCALE_SEGMENT_NAME]}return e}},7999:(e,t,r)=>{var n=r(2115),o=r(1684);function i(){let e=n.useContext(o.IntlContext);if(!e)throw Error(void 0);return e}t.useIntlContext=i,t.useLocale=function(){return i().locale}},9035:(e,t,r)=>{var n=r(6690);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function h(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:h(Intl.DateTimeFormat,e.dateTime),getNumberFormat:h(Intl.NumberFormat,e.number),getPluralRules:h(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:h(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:h(Intl.ListFormat,e.list),getDisplayNames:h(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s},9869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9946:(e,t,r)=>{r.d(t,{A:()=>h});var n=r(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:h,className:l="",children:u,iconNode:c,...f}=e;return(0,n.createElement)("svg",{ref:t,...a,width:o,height:o,stroke:r,strokeWidth:h?24*Number(s)/Number(o):s,className:i("lucide",l),...f},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),h=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:h,...l}=r;return(0,n.createElement)(s,{ref:a,iconNode:t,className:i("lucide-".concat(o(e)),h),...l})});return r.displayName="".concat(e),r}}}]);