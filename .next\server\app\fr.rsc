1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
6:I[9665,[],"OutletBoundary"]
9:I[4911,[],"AsyncMetadataOutlet"]
b:I[9665,[],"ViewportBoundary"]
d:I[9665,[],"MetadataBoundary"]
f:I[6614,[],""]
:HL["/_next/static/css/9af4fd01e4b7f88e.css","style"]
0:{"P":null,"b":"_9dcwiYmTh5Vy-pSnKxJ_","p":"","c":["","fr"],"i":false,"f":[[["",{"children":[["locale","fr","d"],{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/9af4fd01e4b7f88e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":[["locale","fr","d"],["$","$1","c",{"children":[null,"$L4"]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L5",null,["$","$L6",null,{"children":["$L7","$L8",["$","$L9",null,{"promise":"$@a"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","5LiDFvgFaPZtUwzLigVtEv",{"children":[["$","$Lb",null,{"children":"$Lc"}],null]}],["$","$Ld",null,{"children":"$Le"}]]}],false]],"m":"$undefined","G":["$f","$undefined"],"s":false,"S":true}
10:"$Sreact.suspense"
11:I[4911,[],"AsyncMetadata"]
e:["$","div",null,{"hidden":true,"children":["$","$10",null,{"fallback":null,"children":["$","$L11",null,{"promise":"$@12"}]}]}]
8:null
13:I[9295,["699","static/chunks/699-416c0b5f86a762f0.js","465","static/chunks/app/%5Blocale%5D/page-5c18142be3a734de.js"],"default",1]
5:["$","div",null,{"className":"space-y-8","children":[["$","$L13",null,{}],["$","div",null,{"className":"max-w-4xl mx-auto grid md:grid-cols-3 gap-6 mt-12","children":[["$","div",null,{"className":"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"className":"w-6 h-6 text-blue-600 dark:text-blue-400","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M13 10V3L4 14h7v7l9-11h-7z"}]}]}],["$","h3",null,{"className":"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2","children":"Fast Processing"}],["$","p",null,{"className":"text-gray-600 dark:text-gray-400 text-sm","children":"Client-side processing means your videos never leave your device, ensuring privacy and speed."}]]}],["$","div",null,{"className":"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"className":"w-6 h-6 text-green-600 dark:text-green-400","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"}]}]}],["$","h3",null,{"className":"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2","children":"Perfect Sizing"}],["$","p",null,{"className":"text-gray-600 dark:text-gray-400 text-sm","children":"Automatically resizes videos to 512x512 pixels while preserving aspect ratio with smart padding."}]]}],["$","div",null,{"className":"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4","children":["$","svg",null,{"className":"w-6 h-6 text-purple-600 dark:text-purple-400","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"}]}]}],["$","h3",null,{"className":"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2","children":"Privacy First"}],["$","p",null,{"className":"text-gray-600 dark:text-gray-400 text-sm","children":"All processing happens in your browser. No uploads to servers, no data collection."}]]}]]}],["$","div",null,{"className":"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6","children":[["$","h2",null,{"className":"text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4","children":"How to Use"}],["$","ol",null,{"className":"space-y-3 text-gray-600 dark:text-gray-400","children":[["$","li",null,{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium","children":"1"}],["$","span",null,{"children":"Étape 1 : Téléchargez votre fichier MP4"}]]}],["$","li",null,{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium","children":"2"}],["$","span",null,{"children":"Étape 2 : Attendez la conversion automatique"}]]}],["$","li",null,{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium","children":"3"}],["$","span",null,{"children":"Étape 3 : Téléchargez votre nouveau fichier WebM"}]]}],["$","li",null,{"className":"flex items-start gap-3","children":[["$","span",null,{"className":"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium","children":"4"}],["$","span",null,{"children":"Download your converted WebM video file"}]]}]]}]]}]]}]
4:["$","html",null,{"lang":"fr","children":[["$","head",null,{"children":[["$","meta",null,{"charSet":"utf-8"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","link",null,{"rel":"icon","href":"/favicon.ico"}]]}],["$","body",null,{"className":"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800","children":"$L14"}]]}]
15:I[2103,["450","static/chunks/app/%5Blocale%5D/layout-5d730d9d8e25eb22.js"],"default"]
14:["$","$L15",null,{"locale":"fr","now":"$D2025-06-25T04:37:06.244Z","timeZone":"America/Mexico_City","messages":{"title":"Convertisseur WebM Billie","subtitle":"Convertissez des vidéos MP4 au format WebM avec redimensionnement 512x512","steps":{"step1":"Étape 1 : Téléchargez votre fichier MP4","step2":"Étape 2 : Attendez la conversion automatique","step3":"Étape 3 : Téléchargez votre nouveau fichier WebM"},"upload":{"dragDrop":"Glissez et déposez votre fichier vidéo ici","or":"ou","clickToSelect":"Cliquez pour sélectionner un fichier","maxSize":"Taille maximale : 100 Mo","supportedFormats":"Supporte MP4, AVI, MOV, MKV, WebM et plus","uploading":"Téléchargement en cours...","processing":"Conversion de la vidéo...","success":"Conversion terminée !","downloadReady":"Votre fichier WebM est prêt à être téléchargé"},"buttons":{"selectFile":"Sélectionner un fichier","download":"Télécharger WebM","convertAnother":"Convertir un autre fichier","cancel":"Annuler"},"errors":{"unsupportedFormat":"Format vidéo non supporté. Veuillez télécharger un fichier vidéo commun (MP4, AVI, MOV, etc.)","fileTooLarge":"Fichier trop volumineux. Veuillez télécharger une vidéo de moins de 100 Mo.","conversionFailed":"La conversion a échoué. Veuillez réessayer.","uploadFailed":"Le téléchargement a échoué. Veuillez réessayer.","noFileSelected":"Aucun fichier sélectionné. Veuillez choisir un fichier vidéo."},"language":{"switch":"Passer à l'anglais","current":"Français"},"progress":{"uploading":"Téléchargement : {progress}%","converting":"Conversion : {progress}%","preparing":"Préparation de la conversion...","finalizing":"Finalisation..."}},"children":["$","div",null,{"className":"container mx-auto px-4 py-8","children":["$","main",null,{"children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]
c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
7:null
a:{"metadata":[["$","title","0",{"children":"Billie WebM Converter"}],["$","meta","1",{"name":"description","content":"A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing"}],["$","meta","2",{"name":"author","content":"Billie WebM Converter"}],["$","meta","3",{"name":"keywords","content":"video converter, webm, ffmpeg, online converter, video compression"}]],"error":null,"digest":"$undefined"}
12:{"metadata":"$a:metadata","error":null,"digest":"$undefined"}
