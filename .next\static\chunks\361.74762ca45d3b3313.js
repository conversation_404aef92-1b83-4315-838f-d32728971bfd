(()=>{var e={260:e=>{function r(e){return Promise.resolve().then(()=>{var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r})}r.keys=()=>[],r.resolve=r,r.id=260,e.exports=r}},r={};function t(a){var s=r[a];if(void 0!==s)return s.exports;var E=r[a]={exports:{}},o=!0;try{e[a](E,E.exports,t),o=!1}finally{o&&delete r[a]}return E.exports}t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),(()=>{"use strict";var e;let r,a="https://unpkg.com/@ffmpeg/core@0.12.9/dist/umd/ffmpeg-core.js";!function(e){e.LOAD="LOAD",e.EXEC="EXEC",e.FFPROBE="FFPROBE",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT"}(e||(e={}));let s=Error("unknown message type"),E=Error("ffmpeg is not loaded, call `await ffmpeg.load()` first");Error("called FFmpeg.terminate()");let o=Error("failed to import ffmpeg-core.js"),l=async({coreURL:s,wasmURL:E,workerURL:l})=>{let i=!r;try{s||(s=a),importScripts(s)}catch{if(s&&s!==a||(s=a.replace("/umd/","/esm/")),self.createFFmpegCore=(await t(260)(s)).default,!self.createFFmpegCore)throw o}let c=s,n=E||s.replace(/.js$/g,".wasm"),R=l||s.replace(/.js$/g,".worker.js");return(r=await self.createFFmpegCore({mainScriptUrlOrBlob:`${c}#${btoa(JSON.stringify({wasmURL:n,workerURL:R}))}`})).setLogger(r=>self.postMessage({type:e.LOG,data:r})),r.setProgress(r=>self.postMessage({type:e.PROGRESS,data:r})),i},i=({args:e,timeout:t=-1})=>{r.setTimeout(t),r.exec(...e);let a=r.ret;return r.reset(),a},c=({args:e,timeout:t=-1})=>{r.setTimeout(t),r.ffprobe(...e);let a=r.ret;return r.reset(),a},n=({path:e,data:t})=>(r.FS.writeFile(e,t),!0),R=({path:e,encoding:t})=>r.FS.readFile(e,{encoding:t}),f=({path:e})=>(r.FS.unlink(e),!0),F=({oldPath:e,newPath:t})=>(r.FS.rename(e,t),!0),p=({path:e})=>(r.FS.mkdir(e),!0),O=({path:e})=>{let t=r.FS.readdir(e),a=[];for(let s of t){let t=r.FS.stat(`${e}/${s}`),E=r.FS.isDir(t.mode);a.push({name:s,isDir:E})}return a},m=({path:e})=>(r.FS.rmdir(e),!0),L=({fsType:e,options:t,mountPoint:a})=>{let s=r.FS.filesystems[e];return!!s&&(r.FS.mount(s,t,a),!0)},u=({mountPoint:e})=>(r.FS.unmount(e),!0);self.onmessage=async({data:{id:t,type:a,data:o}})=>{let D,d=[];try{if(a!==e.LOAD&&!r)throw E;switch(a){case e.LOAD:D=await l(o);break;case e.EXEC:D=i(o);break;case e.FFPROBE:D=c(o);break;case e.WRITE_FILE:D=n(o);break;case e.READ_FILE:D=R(o);break;case e.DELETE_FILE:D=f(o);break;case e.RENAME:D=F(o);break;case e.CREATE_DIR:D=p(o);break;case e.LIST_DIR:D=O(o);break;case e.DELETE_DIR:D=m(o);break;case e.MOUNT:D=L(o);break;case e.UNMOUNT:D=u(o);break;default:throw s}}catch(r){self.postMessage({id:t,type:e.ERROR,data:r.toString()});return}D instanceof Uint8Array&&d.push(D.buffer),self.postMessage({id:t,type:a,data:D},d)}})(),_N_E={}})();