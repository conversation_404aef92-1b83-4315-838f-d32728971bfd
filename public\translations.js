const translations = {
    en: {
        'main-title': 'Billie WebM Converter',
        'subtitle': 'Convert videos to WebM format with 512x512 resizing',
        'language-text': 'Switch to French',
        'step1': 'Step 1: Upload your video file',
        'step2': 'Step 2: Wait for automatic conversion',
        'step3': 'Step 3: Download your new WebM file',
        'upload-text': 'Drag and drop your video file here',
        'or-text': 'or',
        'click-text': 'Click to select file',
        'max-size': 'Max file size: 100MB',
        'supported-format': 'Supported formats: MP4, AVI, MOV, MKV, WebM, and more',
        'progress-text': 'Converting video...',
        'success-title': 'Conversion complete!',
        'success-message': 'Your WebM file is ready for download',
        'download-text': 'Download WebM',
        'convert-another-text': 'Convert Another File',
        'errors': {
            'unsupported-format': 'Unsupported file format. Please select a common video file (MP4, AVI, MOV, MKV, etc.).',
            'file-too-large': 'File is too large. Maximum size is 100MB.',
            'conversion-failed': 'Conversion failed. Please try again.',
            'upload-failed': 'Upload failed. Please try again.',
            'no-file-selected': 'No file selected. Please choose a video file.'
        },
        'progress': {
            'preparing': 'Preparing conversion...',
            'converting': 'Converting video...',
            'finalizing': 'Finalizing...'
        }
    },
    fr: {
        'main-title': 'Convertisseur WebM Billie',
        'subtitle': 'Convertissez des vidéos au format WebM avec redimensionnement 512x512',
        'language-text': 'Passer à l\'anglais',
        'step1': 'Étape 1 : Téléchargez votre fichier vidéo',
        'step2': 'Étape 2 : Attendez la conversion automatique',
        'step3': 'Étape 3 : Téléchargez votre nouveau fichier WebM',
        'upload-text': 'Glissez et déposez votre fichier vidéo ici',
        'or-text': 'ou',
        'click-text': 'Cliquez pour sélectionner un fichier',
        'max-size': 'Taille maximale : 100 Mo',
        'supported-format': 'Formats supportés : MP4, AVI, MOV, MKV, WebM, et plus',
        'progress-text': 'Conversion de la vidéo...',
        'success-title': 'Conversion terminée !',
        'success-message': 'Votre fichier WebM est prêt à être téléchargé',
        'download-text': 'Télécharger WebM',
        'convert-another-text': 'Convertir un autre fichier',
        'errors': {
            'unsupported-format': 'Format de fichier non supporté. Veuillez sélectionner un fichier vidéo commun (MP4, AVI, MOV, MKV, etc.).',
            'file-too-large': 'Le fichier est trop volumineux. La taille maximale est de 100 Mo.',
            'conversion-failed': 'La conversion a échoué. Veuillez réessayer.',
            'upload-failed': 'Le téléchargement a échoué. Veuillez réessayer.',
            'no-file-selected': 'Aucun fichier sélectionné. Veuillez choisir un fichier vidéo.'
        },
        'progress': {
            'preparing': 'Préparation de la conversion...',
            'converting': 'Conversion de la vidéo...',
            'finalizing': 'Finalisation...'
        }
    }
};

let currentLanguage = localStorage.getItem('language') || 'en';

function updateLanguage(lang) {
    currentLanguage = lang;
    localStorage.setItem('language', lang);
    document.documentElement.lang = lang;
    
    // Update all translatable elements
    Object.keys(translations[lang]).forEach(key => {
        const element = document.getElementById(key);
        if (element && typeof translations[lang][key] === 'string') {
            element.textContent = translations[lang][key];
        }
    });
}

function t(key, params = {}) {
    let translation = translations[currentLanguage];
    const keys = key.split('.');
    
    for (const k of keys) {
        if (translation && typeof translation === 'object') {
            translation = translation[k];
        } else {
            return key; // Return key if translation not found
        }
    }
    
    if (typeof translation === 'string') {
        // Replace parameters in translation
        return translation.replace(/\{(\w+)\}/g, (match, param) => {
            return params[param] || match;
        });
    }
    
    return key;
}

// Initialize language on page load
document.addEventListener('DOMContentLoaded', () => {
    updateLanguage(currentLanguage);
});
