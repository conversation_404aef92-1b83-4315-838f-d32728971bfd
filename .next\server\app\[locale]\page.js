/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NvZHklNUMlNUNEZXNrdG9wJTVDJTVDQmlsbGllJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBeUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxDb2R5XFxcXERlc2t0b3BcXFxcQmlsbGllXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VideoConverter.tsx */ \"(rsc)/./src/components/VideoConverter.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NvZHklNUMlNUNEZXNrdG9wJTVDJTVDQmlsbGllJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1ZpZGVvQ29udmVydGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXENvZHlcXFxcRGVza3RvcFxcXFxCaWxsaWVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVmlkZW9Db252ZXJ0ZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../i18n */ \"(rsc)/./src/i18n.ts\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    // Validate that the incoming `locale` parameter is valid\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.includes(locale)) (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    // Enable static rendering\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__.setCachedRequestLocale)(locale);\n    // Providing all messages to the client\n    // side is the easiest way to get started\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_VideoConverter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/VideoConverter */ \"(rsc)/./src/components/VideoConverter.tsx\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n\n\n\n\nasync function Home({ params }) {\n    const { locale } = await params;\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_2__.setCachedRequestLocale)(locale);\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoConverter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto grid md:grid-cols-3 gap-6 mt-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Fast Processing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                children: \"Client-side processing means your videos never leave your device, ensuring privacy and speed.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-green-600 dark:text-green-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Perfect Sizing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                children: \"Automatically resizes videos to 512x512 pixels while preserving aspect ratio with smart padding.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-purple-600 dark:text-purple-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Privacy First\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                children: \"All processing happens in your browser. No uploads to servers, no data collection.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4\",\n                        children: \"How to Use\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"space-y-3 text-gray-600 dark:text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('steps.step1')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('steps.step2')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('steps.step3')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\n                                        children: \"4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Download your converted WebM video file\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8c96b3143d7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXENvZHlcXERlc2t0b3BcXEJpbGxpZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZThjOTZiMzE0M2Q3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\nconst metadata = {\n    title: 'Billie WebM Converter',\n    description: 'A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing',\n    keywords: 'video converter, webm, ffmpeg, online converter, video compression',\n    authors: [\n        {\n            name: 'Billie WebM Converter'\n        }\n    ]\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNzQjtBQUVmLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtJQUNWQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUF3QjtLQUFFO0FBQzlDLEVBQUM7QUFFTSxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXENvZHlcXERlc2t0b3BcXEJpbGxpZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSwgVmlld3BvcnQgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0JpbGxpZSBXZWJNIENvbnZlcnRlcicsXG4gIGRlc2NyaXB0aW9uOiAnQSBzaW1wbGUgYW5kIHVzZXItZnJpZW5kbHkgb25saW5lIHRvb2wgdG8gY29udmVydCB2aWRlb3MgdG8gV2ViTSBmb3JtYXQgd2l0aCA1MTJ4NTEyIHJlc2l6aW5nJyxcbiAga2V5d29yZHM6ICd2aWRlbyBjb252ZXJ0ZXIsIHdlYm0sIGZmbXBlZywgb25saW5lIGNvbnZlcnRlciwgdmlkZW8gY29tcHJlc3Npb24nLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnQmlsbGllIFdlYk0gQ29udmVydGVyJyB9XSxcbn1cblxuZXhwb3J0IGNvbnN0IHZpZXdwb3J0OiBWaWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gY2hpbGRyZW5cbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJ3aWR0aCIsImluaXRpYWxTY2FsZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Billie\\src\\components\\VideoConverter.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n// Can be imported from a shared config\nconst locales = [\n    'en',\n    'fr'\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !locales.includes(locale)) {\n        locale = 'en';\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDa0Q7QUFFbEQsdUNBQXVDO0FBQ3ZDLE1BQU1DLFVBQVU7SUFBQztJQUFNO0NBQUs7QUFFNUIsaUVBQWVELDREQUFnQkEsQ0FBQyxPQUFPLEVBQUNFLGFBQWEsRUFBQztJQUNwRCx1REFBdUQ7SUFDdkQsSUFBSUMsU0FBUyxNQUFNRDtJQUVuQixxQ0FBcUM7SUFDckMsSUFBSSxDQUFDQyxVQUFVLENBQUNGLFFBQVFHLFFBQVEsQ0FBQ0QsU0FBZ0I7UUFDL0NBLFNBQVM7SUFDWDtJQUVBLE9BQU87UUFDTEE7UUFDQUUsVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBYSxFQUFFRixPQUFPLE1BQU0sR0FBR0csT0FBTztJQUNoRTtBQUNGLEVBQUUsRUFBQztBQUVjIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXENvZHlcXERlc2t0b3BcXEJpbGxpZVxcc3JjXFxpMThuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7bm90Rm91bmR9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQge2dldFJlcXVlc3RDb25maWd9IGZyb20gJ25leHQtaW50bC9zZXJ2ZXInO1xuXG4vLyBDYW4gYmUgaW1wb3J0ZWQgZnJvbSBhIHNoYXJlZCBjb25maWdcbmNvbnN0IGxvY2FsZXMgPSBbJ2VuJywgJ2ZyJ107XG5cbmV4cG9ydCBkZWZhdWx0IGdldFJlcXVlc3RDb25maWcoYXN5bmMgKHtyZXF1ZXN0TG9jYWxlfSkgPT4ge1xuICAvLyBUaGlzIHR5cGljYWxseSBjb3JyZXNwb25kcyB0byB0aGUgYFtsb2NhbGVdYCBzZWdtZW50XG4gIGxldCBsb2NhbGUgPSBhd2FpdCByZXF1ZXN0TG9jYWxlO1xuXG4gIC8vIEVuc3VyZSB0aGF0IGEgdmFsaWQgbG9jYWxlIGlzIHVzZWRcbiAgaWYgKCFsb2NhbGUgfHwgIWxvY2FsZXMuaW5jbHVkZXMobG9jYWxlIGFzIGFueSkpIHtcbiAgICBsb2NhbGUgPSAnZW4nO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBsb2NhbGUsXG4gICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcbiAgfTtcbn0pO1xuXG5leHBvcnQge2xvY2FsZXN9O1xuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJsb2NhbGVzIiwicmVxdWVzdExvY2FsZSIsImxvY2FsZSIsImluY2x1ZGVzIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NvZHklNUMlNUNEZXNrdG9wJTVDJTVDQmlsbGllJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBeUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxDb2R5XFxcXERlc2t0b3BcXFxcQmlsbGllXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VideoConverter.tsx */ \"(ssr)/./src/components/VideoConverter.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0NvZHklNUMlNUNEZXNrdG9wJTVDJTVDQmlsbGllJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1ZpZGVvQ29udmVydGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF1SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXENvZHlcXFxcRGVza3RvcFxcXFxCaWxsaWVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVmlkZW9Db252ZXJ0ZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CCody%5C%5CDesktop%5C%5CBillie%5C%5Csrc%5C%5Ccomponents%5C%5CVideoConverter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/VideoConverter.tsx":
/*!*******************************************!*\
  !*** ./src/components/VideoConverter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoConverter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ffmpeg/ffmpeg */ \"@ffmpeg/ffmpeg\");\n/* harmony import */ var _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ffmpeg/util */ \"@ffmpeg/util\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Download,Globe,Play,RotateCcw,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__, _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__]);\n([_ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__, _ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction VideoConverter() {\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conversion, setConversion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stage: 'idle',\n        progress: 0\n    });\n    const [convertedVideo, setConvertedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ffmpegLoaded, setFfmpegLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ffmpegRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const switchLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[switchLocale]\": ()=>{\n            const newLocale = locale === 'en' ? 'fr' : 'en';\n            const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);\n            router.push(newPath);\n        }\n    }[\"VideoConverter.useCallback[switchLocale]\"], [\n        locale,\n        pathname,\n        router\n    ]);\n    const loadFFmpeg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[loadFFmpeg]\": async ()=>{\n            if (ffmpegRef.current || ffmpegLoaded) return;\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 10\n                });\n                const ffmpeg = new _ffmpeg_ffmpeg__WEBPACK_IMPORTED_MODULE_2__.FFmpeg();\n                ffmpegRef.current = ffmpeg;\n                // Load FFmpeg with progress tracking\n                ffmpeg.on('log', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": ({ message })=>{\n                        console.log('FFmpeg log:', message);\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                ffmpeg.on('progress', {\n                    \"VideoConverter.useCallback[loadFFmpeg]\": ({ progress })=>{\n                        if (conversion.stage === 'converting') {\n                            const adjustedProgress = 50 + progress * 40 // 50-90%\n                            ;\n                            setConversion({\n                                \"VideoConverter.useCallback[loadFFmpeg]\": (prev)=>({\n                                        ...prev,\n                                        progress: adjustedProgress\n                                    })\n                            }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                        }\n                    }\n                }[\"VideoConverter.useCallback[loadFFmpeg]\"]);\n                setConversion({\n                    stage: 'loading',\n                    progress: 30\n                });\n                // Load FFmpeg core with stable version\n                const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';\n                await ffmpeg.load({\n                    coreURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),\n                    wasmURL: await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.toBlobURL)(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm')\n                });\n                setFfmpegLoaded(true);\n                setConversion({\n                    stage: 'idle',\n                    progress: 0\n                });\n            } catch (error) {\n                console.error('Failed to load FFmpeg:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[loadFFmpeg]\"], [\n        ffmpegLoaded,\n        conversion.stage\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileSelect]\": (selectedFile)=>{\n            // Validate file type\n            const allowedTypes = [\n                'video/mp4',\n                'video/avi',\n                'video/mov',\n                'video/quicktime',\n                'video/x-msvideo',\n                'video/mkv',\n                'video/x-matroska',\n                'video/webm',\n                'video/3gpp',\n                'video/x-flv',\n                'video/x-ms-wmv'\n            ];\n            const allowedExtensions = [\n                '.mp4',\n                '.avi',\n                '.mov',\n                '.mkv',\n                '.webm',\n                '.3gp',\n                '.flv',\n                '.wmv',\n                '.m4v',\n                '.mpg',\n                '.mpeg',\n                '.ogv'\n            ];\n            const fileName = selectedFile.name.toLowerCase();\n            const hasValidType = allowedTypes.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidType\": (type)=>selectedFile.type.includes(type)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidType\"]);\n            const hasValidExtension = allowedExtensions.some({\n                \"VideoConverter.useCallback[handleFileSelect].hasValidExtension\": (ext)=>fileName.endsWith(ext)\n            }[\"VideoConverter.useCallback[handleFileSelect].hasValidExtension\"]);\n            if (!hasValidType && !hasValidExtension) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.unsupportedFormat')\n                });\n                return;\n            }\n            // Check file size (25MB limit for better memory management)\n            if (selectedFile.size > 25 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.fileTooLarge')\n                });\n                return;\n            }\n            setFile(selectedFile);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n        }\n    }[\"VideoConverter.useCallback[handleFileSelect]\"], []);\n    const convertVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[convertVideo]\": async ()=>{\n            if (!file || !ffmpegRef.current) return;\n            // Check available memory (rough estimate)\n            if (file.size > 10 * 1024 * 1024 && performance.memory && performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: 'Insufficient memory. Please try a smaller file or refresh the page.'\n                });\n                return;\n            }\n            try {\n                setConversion({\n                    stage: 'loading',\n                    progress: 20\n                });\n                const ffmpeg = ffmpegRef.current;\n                // Write input file\n                const inputFileName = `input_${Date.now()}.mp4`;\n                const outputFileName = `output_${Date.now()}.webm`;\n                await ffmpeg.writeFile(inputFileName, await (0,_ffmpeg_util__WEBPACK_IMPORTED_MODULE_3__.fetchFile)(file));\n                setConversion({\n                    stage: 'converting',\n                    progress: 50\n                });\n                // Use the simplest possible conversion to avoid memory issues\n                await ffmpeg.exec([\n                    '-i',\n                    inputFileName,\n                    '-s',\n                    '512x512',\n                    '-b:v',\n                    '300k',\n                    '-b:a',\n                    '64k',\n                    outputFileName\n                ]);\n                setConversion({\n                    stage: 'converting',\n                    progress: 95\n                });\n                // Read output file\n                const data = await ffmpeg.readFile(outputFileName);\n                const blob = new Blob([\n                    data\n                ], {\n                    type: 'video/webm'\n                });\n                setConvertedVideo(blob);\n                setConversion({\n                    stage: 'completed',\n                    progress: 100\n                });\n                // Clean up\n                try {\n                    await ffmpeg.deleteFile(inputFileName);\n                    await ffmpeg.deleteFile(outputFileName);\n                } catch (cleanupError) {\n                    console.log('Cleanup completed');\n                }\n            } catch (error) {\n                console.error('Conversion error:', error);\n                setConversion({\n                    stage: 'error',\n                    progress: 0,\n                    error: t('errors.conversionFailed')\n                });\n            }\n        }\n    }[\"VideoConverter.useCallback[convertVideo]\"], [\n        file,\n        t\n    ]);\n    const downloadVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[downloadVideo]\": ()=>{\n            if (!convertedVideo) return;\n            const url = URL.createObjectURL(convertedVideo);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = `converted-${Date.now()}.webm`;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"VideoConverter.useCallback[downloadVideo]\"], [\n        convertedVideo\n    ]);\n    const resetConverter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[resetConverter]\": async ()=>{\n            setFile(null);\n            setConvertedVideo(null);\n            setConversion({\n                stage: 'idle',\n                progress: 0\n            });\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Clean up FFmpeg memory if there was an error\n            if (ffmpegRef.current && conversion.stage === 'error') {\n                try {\n                    // Try to clean up any remaining files\n                    await ffmpegRef.current.deleteFile('input.mp4').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                    await ffmpegRef.current.deleteFile('output.webm').catch({\n                        \"VideoConverter.useCallback[resetConverter]\": ()=>{}\n                    }[\"VideoConverter.useCallback[resetConverter]\"]);\n                } catch (error) {\n                    console.log('Cleanup completed');\n                }\n            }\n        }\n    }[\"VideoConverter.useCallback[resetConverter]\"], [\n        conversion.stage\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"VideoConverter.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"VideoConverter.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const droppedFiles = Array.from(e.dataTransfer.files);\n            if (droppedFiles.length > 0) {\n                handleFileSelect(droppedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleDrop]\"], [\n        handleFileSelect\n    ]);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VideoConverter.useCallback[handleFileInputChange]\": (e)=>{\n            const selectedFiles = e.target.files;\n            if (selectedFiles && selectedFiles.length > 0) {\n                handleFileSelect(selectedFiles[0]);\n            }\n        }\n    }[\"VideoConverter.useCallback[handleFileInputChange]\"], [\n        handleFileSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-800 dark:text-white mb-2\",\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: t('subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: switchLocale,\n                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            t('language.switch')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            !file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `upload-area p-8 text-center cursor-pointer ${isDragOver ? 'dragover' : ''}`,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                onClick: ()=>fileInputRef.current?.click(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                        children: t('upload.dragDrop')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                        children: t('upload.or')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-600 dark:text-blue-400 mb-4\",\n                        children: t('upload.clickToSelect')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: t('upload.maxSize')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"video/*\",\n                        onChange: handleFileInputChange,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this),\n            file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                                children: \"Selected File:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-500\",\n                                children: [\n                                    \"Size: \",\n                                    (file.size / (1024 * 1024)).toFixed(2),\n                                    \" MB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            conversion.stage === 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ffmpegLoaded ? convertVideo : loadFFmpeg,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this),\n                                            ffmpegLoaded ? t('buttons.selectFile') : 'Load Converter'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.cancel')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadVideo,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.download')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetConverter,\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            t('buttons.convertAnother')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this),\n            (conversion.stage === 'loading' || conversion.stage === 'converting') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: conversion.stage === 'loading' ? t('progress.preparing') : t('upload.processing')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    Math.round(conversion.progress),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"progress-bar bg-blue-600 h-2 rounded-full\",\n                            style: {\n                                width: `${conversion.progress}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Download_Globe_Play_RotateCcw_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 dark:text-red-200\",\n                                    children: \"Conversion Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600 dark:text-red-300 mt-1\",\n                                    children: conversion.error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetConverter,\n                                    className: \"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors\",\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 374,\n                columnNumber: 9\n            }, this),\n            conversion.stage === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-3 w-3 text-white\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-green-800 dark:text-green-200\",\n                                    children: t('upload.success')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600 dark:text-green-300\",\n                                    children: t('upload.downloadReady')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VideoConverter.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@ffmpeg/ffmpeg":
/*!*********************************!*\
  !*** external "@ffmpeg/ffmpeg" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@ffmpeg/ffmpeg");;

/***/ }),

/***/ "@ffmpeg/util":
/*!*******************************!*\
  !*** external "@ffmpeg/util" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("@ffmpeg/util");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@formatjs","vendor-chunks/next-intl","vendor-chunks/use-intl","vendor-chunks/lucide-react","vendor-chunks/intl-messageformat","vendor-chunks/tslib"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCody%5CDesktop%5CBillie&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();