(()=>{var e={};e.id=465,e.ids=[465],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(1042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},444:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>l,FormatError:()=>eo,IntlMessageFormat:()=>eh,InvalidValueError:()=>ei,InvalidValueTypeError:()=>ea,MissingValueError:()=>es,PART_TYPE:()=>u,default:()=>ef,formatToParts:()=>eu,isFormatXMLElementFn:()=>el});var n,o,i,a,s,l,u,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function d(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,r(9550));function m(e){return e.type===o.literal}function g(e){return e.type===o.number}function b(e){return e.type===o.date}function E(e){return e.type===o.time}function y(e){return e.type===o.select}function v(e){return e.type===o.plural}function _(e){return e.type===o.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var H=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,R=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,N=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,S=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,P=/^(@+)?(\+|#+)?[rs]?$/g,B=/(\*)(0+)|(#+)(0+)|(0+)/g,I=/^(0+)$/;function w(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(P,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function C(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function O(e){var t=C(e);return t||{}}var L={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},M=new RegExp("^".concat(H.source,"*")),x=new RegExp("".concat(H.source,"*$"));function k(e,t){return{start:e,end:t}}var F=!!String.prototype.startsWith&&"_a".startsWith("a",1),D=!!String.fromCodePoint,j=!!Object.fromEntries,G=!!String.prototype.codePointAt,U=!!String.prototype.trimStart,V=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},K=!0;try{K=(null==(a=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){K=!1}var Z=F?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},q=D?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},z=j?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},Y=G?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},W=U?function(e){return e.trimStart()}:function(e){return e.replace(M,"")},$=V?function(e){return e.trimEnd()}:function(e){return e.replace(x,"")};function Q(e,t){return new RegExp(e,t)}if(K){var J=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return J.lastIndex=t,null!=(r=J.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=Y(e,t);if(void 0===o||er(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return q.apply(void 0,r)};var ee=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),i.push({type:o.pound,location:k(l,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&et(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,k(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:k(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,k(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,k(r,this.clonePosition()));if(this.isEOF()||!et(this.char()))return this.error(n.INVALID_TAG,k(l,this.clonePosition()));var u=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,k(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:k(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,k(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var l=k(r,this.clonePosition());return{val:{type:o.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(et(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return q.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),q(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,k(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,k(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:k(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,k(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:k(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,k(l,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var d=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=$(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,k(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:k(d,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var b=k(a,this.clonePosition());if(h&&Z(null==h?void 0:h.style,"::",0)){var E=W(h.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(E,h.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:b,style:p.val},err:null}}if(0===E.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,b);var y,v=E;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(L[t||""]||L[n||""]||L["".concat(n,"-001")]||L["001"])[0]}(t);for(("H"==l||"k"==l)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=l+r}else"J"===o?r+="H":r+=o}return r}(E,this.locale));var m={type:i.dateTime,pattern:v,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},v.replace(R,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===u?o.date:o.time,value:r,location:b,style:m},err:null}}return{val:{type:"number"===u?o.number:"date"===u?o.date:o.time,value:r,location:b,style:null!=(s=null==h?void 0:h.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,k(_,f({},_)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==u&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,k(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=p.val}var H=this.tryParsePluralOrSelectOptions(e,u,t,T);if(H.err)return H;var g=this.tryParseArgumentClose(a);if(g.err)return g;var N=k(a,this.clonePosition());if("select"===u)return{val:{type:o.select,value:r,options:z(H.val),location:N},err:null};return{val:{type:o.plural,value:r,options:z(H.val),offset:A,pluralType:"plural"===u?"cardinal":"ordinal",location:N},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,k(l,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,k(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(N).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(B,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(I.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(S.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(S,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),w(o)));continue}if(P.test(n.stem)){t=f(f({},t),w(n.stem));continue}var i=C(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!I.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],l=new Set,u=o.value,c=o.location;;){if(0===u.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=k(h,this.clonePosition()),u=this.message.slice(h.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(a=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,k(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(d);if(m.err)return m;s.push([u,{value:p.val,location:k(d,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,k(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,k(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=k(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Y(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&er(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function et(e){return e>=97&&e<=122||e>=65&&e<=90}function er(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function en(e,t){void 0===t&&(t={});var r=new ee(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,y(t)||v(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else g(t)&&T(t.style)||(b(t)||E(t))&&A(t.style)?delete t.style.location:_(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var eo=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ei=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,o)||this}return h(t,e),t}(eo),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return h(t,e),t}(eo),es=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return h(t,e),t}(eo);function el(e){return"function"==typeof e}function eu(e,t,r,n,i,a,s){if(1===e.length&&m(e[0]))return[{type:u.literal,value:e[0].value}];for(var c=[],h=0;h<e.length;h++){var f=e[h];if(m(f)){c.push({type:u.literal,value:f.value});continue}if(f.type===o.pound){"number"==typeof a&&c.push({type:u.literal,value:r.getNumberFormat(t).format(a)});continue}var d=f.value;if(!(i&&d in i))throw new es(d,s);var p=i[d];if(f.type===o.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),c.push({type:"string"==typeof p?u.literal:u.object,value:p});continue}if(b(f)){var H="string"==typeof f.style?n.date[f.style]:A(f.style)?f.style.parsedOptions:void 0;c.push({type:u.literal,value:r.getDateTimeFormat(t,H).format(p)});continue}if(E(f)){var H="string"==typeof f.style?n.time[f.style]:A(f.style)?f.style.parsedOptions:n.time.medium;c.push({type:u.literal,value:r.getDateTimeFormat(t,H).format(p)});continue}if(g(f)){var H="string"==typeof f.style?n.number[f.style]:T(f.style)?f.style.parsedOptions:void 0;H&&H.scale&&(p*=H.scale||1),c.push({type:u.literal,value:r.getNumberFormat(t,H).format(p)});continue}if(_(f)){var R=f.children,N=f.value,S=i[N];if(!el(S))throw new ea(N,"function",s);var P=S(eu(R,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(P)||(P=[P]),c.push.apply(c,P.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(y(f)){var B=f.options[p]||f.options.other;if(!B)throw new ei(f.value,p,Object.keys(f.options),s);c.push.apply(c,eu(B.value,t,r,n,i));continue}if(v(f)){var B=f.options["=".concat(p)];if(!B){if(!Intl.PluralRules)throw new eo('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,s);var I=r.getPluralRules(t,{type:f.pluralType}).select(p-(f.offset||0));B=f.options[I]||f.options.other}if(!B)throw new ei(f.value,p,Object.keys(f.options),s);c.push.apply(c,eu(B.value,t,r,n,i,p-(f.offset||0)));continue}}return c.length<2?c:c.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ec(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var eh=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return eu(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=s.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=o||{},c=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(l,["formatters"]));this.ast=e.__parse(t,f(f({},c),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.number),strategy:p.strategies.variadic}),getDateTimeFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.dateTime),strategy:p.strategies.variadic}),getPluralRules:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.pluralRules),strategy:p.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=en,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=eh},489:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(6189),o=r(4792),i=r(293);t.default=function(){let e,t=n.useParams();try{e=o.useLocale()}catch(r){if("string"!=typeof(null==t?void 0:t[i.LOCALE_SEGMENT_NAME]))throw r;e=t[i.LOCALE_SEGMENT_NAME]}return e}},511:(e,t,r)=>{"use strict";r.d(t,{A:()=>R});var n=r(9916),o=r(1120),i=r(7133);function a(e){return e.includes("[[...")}function s(e){return e.includes("[...")}function l(e){return e.includes("[")}function u(e){return"function"==typeof e.then}r(9933);var c=r(6280);r(6294);let h="X-NEXT-INTL-LOCALE";var f=r(1999);let d=(0,o.cache)(async function(){let e=(0,c.b)();return u(e)?await e:e}),p=(0,o.cache)(async function(){let e;try{e=(await d()).get(h)||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function m(){return(0,f.U)()||await p()}let g=(0,o.cache)(function(){let e;try{e=(0,c.b)().get(h)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return e||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)()),e});var b=r(8782);let E=!1,y=!1,v=(0,o.cache)(function(){return new Date}),_=(0,o.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),T=(0,o.cache)(async function(e,t){if("function"!=typeof e)throw Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");let r={get locale(){return y||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),y=!0),t||(0,f.U)()||g()},get requestLocale(){return t?Promise.resolve(t):m()}},o=e(r);u(o)&&(o=await o);let i=o.locale;return i||(E||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),E=!0),(i=await r.requestLocale)||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)())),{...o,locale:i,now:o.now||v(),timeZone:o.timeZone||_()}}),A=(0,o.cache)(i.CB),H=(0,o.cache)(i.gZ),R=(0,o.cache)(async function(e){let t=await T(b.A,e);return{...(0,i.TD)(t),_formatters:A(H())}})},693:(e,t,r)=>{Promise.resolve().then(r.bind(r,5529))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(8388),o=r(2637),i=r(1846),a=r(1162),s=r(4971),l=r(8479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1135:()=>{},1158:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1252:()=>{},1437:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},1779:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{default:()=>b});var o=r(687),i=r(3210),a=r(9305),s=r(5056),l=r(1437),u=r(6023),c=r(7840),h=r(3943),f=r(1158),d=r(3613),p=r(3213),m=r(6189),g=e([a,s]);function b(){let[e,t]=(0,i.useState)(null),[r,n]=(0,i.useState)({stage:"idle",progress:0}),[g,b]=(0,i.useState)(null),[E,y]=(0,i.useState)(!1),[v,_]=(0,i.useState)(!1),T=(0,i.useRef)(null),A=(0,i.useRef)(null),H=(0,p.useTranslations)(),R=(0,p.useLocale)(),N=(0,m.useRouter)(),S=(0,m.usePathname)(),P=(0,i.useCallback)(()=>{let e="en"===R?"fr":"en",t=S.replace(`/${R}`,`/${e}`);N.push(t)},[R,S,N]),B=(0,i.useCallback)(async()=>{if(!T.current&&!v)try{n({stage:"loading",progress:10});let e=new a.FFmpeg;T.current=e,e.on("log",({message:e})=>{console.log("FFmpeg log:",e)}),e.on("progress",({progress:e})=>{if("converting"===r.stage){let t=50+40*e;n(e=>({...e,progress:t}))}}),n({stage:"loading",progress:30});let t="https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd";await e.load({coreURL:await (0,s.toBlobURL)(`${t}/ffmpeg-core.js`,"text/javascript"),wasmURL:await (0,s.toBlobURL)(`${t}/ffmpeg-core.wasm`,"application/wasm")}),_(!0),n({stage:"idle",progress:0})}catch(e){console.error("Failed to load FFmpeg:",e),n({stage:"error",progress:0,error:H("errors.conversionFailed")})}},[v,r.stage]),I=(0,i.useCallback)(e=>{let r=e.name.toLowerCase(),o=["video/mp4","video/avi","video/mov","video/quicktime","video/x-msvideo","video/mkv","video/x-matroska","video/webm","video/3gpp","video/x-flv","video/x-ms-wmv"].some(t=>e.type.includes(t)),i=[".mp4",".avi",".mov",".mkv",".webm",".3gp",".flv",".wmv",".m4v",".mpg",".mpeg",".ogv"].some(e=>r.endsWith(e));return o||i?e.size>0x6400000?void n({stage:"error",progress:0,error:H("errors.fileTooLarge")}):void(t(e),b(null),n({stage:"idle",progress:0})):void n({stage:"error",progress:0,error:H("errors.unsupportedFormat")})},[]),w=(0,i.useCallback)(async()=>{if(e&&T.current)try{n({stage:"loading",progress:20});let t=T.current;await t.writeFile("input.mp4",await (0,s.fetchFile)(e)),n({stage:"converting",progress:50}),await t.exec(["-i","input.mp4","-vf","scale=512:512:force_original_aspect_ratio=decrease,pad=512:512:(ow-iw)/2:(oh-ih)/2:black","-c:v","libvpx-vp9","-crf","30","-b:v","0","-b:a","128k","-c:a","libopus","-f","webm","output.webm"]),n({stage:"converting",progress:95});let r=await t.readFile("output.webm"),o=new Blob([r],{type:"video/webm"});b(o),n({stage:"completed",progress:100}),await t.deleteFile("input.mp4"),await t.deleteFile("output.webm")}catch(e){console.error("Conversion error:",e),n({stage:"error",progress:0,error:H("errors.conversionFailed")})}},[e]),C=(0,i.useCallback)(()=>{if(!g)return;let e=URL.createObjectURL(g),t=document.createElement("a");t.href=e,t.download=`converted-${Date.now()}.webm`,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(e)},[g]),O=(0,i.useCallback)(()=>{t(null),b(null),n({stage:"idle",progress:0}),A.current&&(A.current.value="")},[]),L=(0,i.useCallback)(e=>{e.preventDefault(),y(!0)},[]),M=(0,i.useCallback)(e=>{e.preventDefault(),y(!1)},[]),x=(0,i.useCallback)(e=>{e.preventDefault(),y(!1);let t=Array.from(e.dataTransfer.files);t.length>0&&I(t[0])},[I]),k=(0,i.useCallback)(e=>{let t=e.target.files;t&&t.length>0&&I(t[0])},[I]);return(0,o.jsxs)("div",{className:"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-800 dark:text-white mb-2",children:H("title")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:H("subtitle")})]}),(0,o.jsxs)("button",{onClick:P,className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors",children:[(0,o.jsx)(l.A,{className:"h-4 w-4"}),H("language.switch")]})]}),!e&&(0,o.jsxs)("div",{className:`upload-area p-8 text-center cursor-pointer ${E?"dragover":""}`,onDragOver:L,onDragLeave:M,onDrop:x,onClick:()=>A.current?.click(),children:[(0,o.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2",children:H("upload.dragDrop")}),(0,o.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-2",children:H("upload.or")}),(0,o.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400 mb-4",children:H("upload.clickToSelect")}),(0,o.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:H("upload.maxSize")}),(0,o.jsx)("input",{ref:A,type:"file",accept:"video/*",onChange:k,className:"hidden"})]}),e&&(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Selected File:"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.name}),(0,o.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:["Size: ",(e.size/1048576).toFixed(2)," MB"]})]}),(0,o.jsxs)("div",{className:"flex gap-3",children:["idle"===r.stage&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("button",{onClick:v?w:B,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,o.jsx)(c.A,{className:"h-4 w-4"}),v?H("buttons.selectFile"):"Load Converter"]}),(0,o.jsxs)("button",{onClick:O,className:"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,o.jsx)(h.A,{className:"h-4 w-4"}),H("buttons.cancel")]})]}),"completed"===r.stage&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("button",{onClick:C,className:"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,o.jsx)(f.A,{className:"h-4 w-4"}),H("buttons.download")]}),(0,o.jsxs)("button",{onClick:O,className:"bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2",children:[(0,o.jsx)(h.A,{className:"h-4 w-4"}),H("buttons.convertAnother")]})]})]})]}),("loading"===r.stage||"converting"===r.stage)&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"loading"===r.stage?H("progress.preparing"):H("upload.processing")}),(0,o.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(r.progress),"%"]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"progress-bar bg-blue-600 h-2 rounded-full",style:{width:`${r.progress}%`}})})]}),"error"===r.stage&&(0,o.jsx)("div",{className:"mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"flex items-start gap-3",children:[(0,o.jsx)(d.A,{className:"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-medium text-red-800 dark:text-red-200",children:"Conversion Error"}),(0,o.jsx)("p",{className:"text-sm text-red-600 dark:text-red-300 mt-1",children:r.error}),(0,o.jsx)("button",{onClick:O,className:"mt-3 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-1 px-3 rounded transition-colors",children:"Try Again"})]})]})}),"completed"===r.stage&&(0,o.jsx)("div",{className:"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"h-5 w-5 bg-green-500 rounded-full flex items-center justify-center",children:(0,o.jsx)("svg",{className:"h-3 w-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-medium text-green-800 dark:text-green-200",children:H("upload.success")}),(0,o.jsx)("p",{className:"text-sm text-green-600 dark:text-green-300",children:H("upload.downloadReady")})]})]})})]})}[a,s]=g.then?(await g)():g,n()}catch(e){n(e)}})},1999:(e,t,r)=>{"use strict";r.d(t,{I:()=>i,U:()=>o});let n=(0,r(1120).cache)(function(){return{locale:void 0}});function o(){return n().locale}function i(e){n().locale=e}},2101:(e,t,r)=>{"use strict";var n=r(8447),o=r(3210),i=r(9969),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let l=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function c(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function h(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,d={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=l,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?h(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(l.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:d,messagesOrError:p,namespace:m,onError:g,timeZone:b}=e,E=p instanceof u;function y(e,t,r){let n=new u(t,r);return g(n),f({error:n,key:e,namespace:m})}function v(e,u,g){let v,_;if(E)return f({error:p,key:e,namespace:m});try{v=h(d,p,e,m)}catch(t){return y(e,l.MISSING_MESSAGE,t.message)}if("object"==typeof v){let t;return y(e,Array.isArray(v)?l.INVALID_MESSAGE:l.INSUFFICIENT_PATH,t)}let T=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(v,u);if(T)return T;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{_=s.getMessageFormat(v,d,function(e,t){let r=t?{...e,dateTime:c(e.dateTime,t)}:e,n=a.default.formats.date,o=t?c(n,t):n,i=a.default.formats.time,s=t?c(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...g},b),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:b,...t})}})}catch(t){return y(e,l.INVALID_MESSAGE,t.message)}try{let e=_.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return y(e,l.FORMATTING_ERROR,t.message)}}function _(e,t,r){let n=v(e,t,r);return"string"!=typeof n?y(e,l.INVALID_MESSAGE,void 0):n}return _.rich=v,_.markup=(e,t,r)=>{let n=v(e,t,r);if("string"!=typeof n){let t=new u(l.FORMATTING_ERROR,void 0);return g(t),f({error:t,key:e,namespace:m})}return n},_.raw=e=>{if(E)return f({error:p,key:e,namespace:m});try{return h(d,p,e,m)}catch(t){return y(e,l.MISSING_MESSAGE,t.message)}},_.has=e=>{if(E)return!1;try{return h(d,p,e,m),!0}catch(e){return!1}},_}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:c}=e;function h(e){var t;return null!=(t=e)&&t.timeZone||(c?e={...e,timeZone:c}:s(new u(l.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(l.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new u(l.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=h(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function g(){return a||(s(new u(l.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):g(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=g());let l=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(l)),s.numeric="second"===a?"auto":"always";let u=(n=a,Math.round(l/d[n]));return r.getRelativeTimeFormat(o,s).format(u,a)}catch(t){return s(new u(l.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return p(i,null==n?void 0:n.dateTime,n=>(n=h(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},2311:(e,t,r)=>{Promise.resolve().then(r.bind(r,5922))},2508:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(7413),o=r(5529),i=r(1120),a=r(7133),s=r(511),l=(0,i.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let n=await (0,s.A)(r);return(0,a.HM)({...n,namespace:t,messages:n.messages})}),u=r(1999);async function c({params:e}){let{locale:t}=await e;(0,u.I)(t);let r=await l();return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsx)(o.default,{}),(0,n.jsxs)("div",{className:"max-w-4xl mx-auto grid md:grid-cols-3 gap-6 mt-12",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Fast Processing"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Client-side processing means your videos never leave your device, ensuring privacy and speed."})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Perfect Sizing"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Automatically resizes videos to 512x512 pixels while preserving aspect ratio with smart padding."})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Privacy First"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"All processing happens in your browser. No uploads to servers, no data collection."})]})]}),(0,n.jsxs)("div",{className:"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"How to Use"}),(0,n.jsxs)("ol",{className:"space-y-3 text-gray-600 dark:text-gray-400",children:[(0,n.jsxs)("li",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium",children:"1"}),(0,n.jsx)("span",{children:r("steps.step1")})]}),(0,n.jsxs)("li",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium",children:"2"}),(0,n.jsx)("span",{children:r("steps.step2")})]}),(0,n.jsxs)("li",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium",children:"3"}),(0,n.jsx)("span",{children:r("steps.step3")})]}),(0,n.jsxs)("li",{className:"flex items-start gap-3",children:[(0,n.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium",children:"4"}),(0,n.jsx)("span",{children:"Download your converted WebM video file"})]})]})]})]})}},2549:(e,t,r)=>{Promise.resolve().then(r.bind(r,1779))},2575:(e,t,r)=>{Promise.resolve().then(r.bind(r,6682))},2584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(3763);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return n.ReflectAdapter.get(t,a,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(3210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:s="",children:l,iconNode:u,...c},h)=>(0,n.createElement)("svg",{ref:h,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",s),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...a},l)=>(0,n.createElement)(s,{ref:l,iconNode:t,className:i(`lucide-${o(e)}`,r),...a}));return r.displayName=`${e}`,r}},2765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2848:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(7552);function o(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let i=o(0,n.useTranslations);t.useFormatter=o(0,n.useFormatter),t.useTranslations=i,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2848),o=r(489),i=r(9266),a=r(7552);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=o.default,t.NextIntlClientProvider=i.default,Object.keys(a).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}})})},3215:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>f,tree:()=>u});var n=r(5239),o=r(8088),i=r(8170),a=r.n(i),s=r(893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2508)),"C:\\Users\\<USER>\\Desktop\\Billie\\src\\app\\[locale]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4183)),"C:\\Users\\<USER>\\Desktop\\Billie\\src\\app\\[locale]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Billie\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Billie\\src\\app\\[locale]\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3453:(e,t,r)=>{"use strict";var n=r(3210),o=r(5132);function i(){let e=n.useContext(o.IntlContext);if(!e)throw Error(void 0);return e}t.useIntlContext=i,t.useLocale=function(){return i().locale}},3613:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2101),o=r(8591),i=r(9969),a=r(5185),s=r(9053),l=r(3453);r(8447),r(3210),r(7132),r(5132),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=o.createTranslator,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.IntlProvider=a.IntlProvider,t.useFormatter=s.useFormatter,t.useMessages=s.useMessages,t.useNow=s.useNow,t.useTimeZone=s.useTimeZone,t.useTranslations=s.useTranslations,t.useLocale=l.useLocale},3873:e=>{"use strict";e.exports=require("path")},3943:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},4069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return l},appendMutableCookies:function(){return h},areCookiesMutableInCurrentPhase:function(){return p},getModifiedCookieValues:function(){return c},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return d}});let n=r(3158),o=r(3763),i=r(9294),a=r(3033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function h(e,t){let r=c(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,l=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return o.ReflectAdapter.get(e,t,r)}}});return c}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return o.ReflectAdapter.get(e,r,n)}}});return t}function p(e){return"action"===e.phase}function m(e){if(!p((0,a.getExpectedRequestStore)(e)))throw new s}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},4183:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_,generateStaticParams:()=>v});var n=r(7413);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var i=r(1120),a=r.n(i),s=r(5922),l=r(511);let u=(0,i.cache)(async function(){return Promise.resolve((await (0,l.A)()).locale)}),c=(0,i.cache)(async function(e){return(await (0,l.A)(e)).now});async function h(e){return c(null==e?void 0:e.locale)}let f=(0,i.cache)(async function(e){return(await (0,l.A)(e)).timeZone});async function d(e){return f(null==e?void 0:e.locale)}async function p(e){let{locale:t,now:r,timeZone:n,...i}=e;return a().createElement(s.default,o({locale:null!=t?t:await u(),now:null!=r?r:await h(),timeZone:null!=n?n:await d()},i))}var m=r(1999);let g=(0,i.cache)(async function(e){var t=await (0,l.A)(e);if(!t.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return t.messages});async function b(e){return g(null==e?void 0:e.locale)}var E=r(9916),y=r(8782);function v(){return y.I.map(e=>({locale:e}))}async function _({children:e,params:t}){let{locale:r}=await t;y.I.includes(r)||(0,E.notFound)(),(0,m.I)(r);let o=await b();return(0,n.jsxs)("html",{lang:r,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("meta",{charSet:"utf-8"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsx)("body",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,n.jsx)(p,{messages:o,children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsx)("main",{children:e})})})})]})}r(1135)},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n,viewport:()=>o}),r(1135);let n={title:"Billie WebM Converter",description:"A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing",keywords:"video converter, webm, ffmpeg, online converter, video compression",authors:[{name:"Billie WebM Converter"}]},o={width:"device-width",initialScale:1};function i({children:e}){return e}},4727:(e,t,r)=>{"use strict";var n=r(444),o=r(1120),i=r(5327),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let l=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function c(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function h(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,d={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=l,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?h(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(l.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:d,messagesOrError:p,namespace:m,onError:g,timeZone:b}=e,E=p instanceof u;function y(e,t,r){let n=new u(t,r);return g(n),f({error:n,key:e,namespace:m})}function v(e,u,g){let v,_;if(E)return f({error:p,key:e,namespace:m});try{v=h(d,p,e,m)}catch(t){return y(e,l.MISSING_MESSAGE,t.message)}if("object"==typeof v){let t;return y(e,Array.isArray(v)?l.INVALID_MESSAGE:l.INSUFFICIENT_PATH,t)}let T=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(v,u);if(T)return T;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{_=s.getMessageFormat(v,d,function(e,t){let r=t?{...e,dateTime:c(e.dateTime,t)}:e,n=a.default.formats.date,o=t?c(n,t):n,i=a.default.formats.time,s=t?c(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...g},b),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:b,...t})}})}catch(t){return y(e,l.INVALID_MESSAGE,t.message)}try{let e=_.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return y(e,l.FORMATTING_ERROR,t.message)}}function _(e,t,r){let n=v(e,t,r);return"string"!=typeof n?y(e,l.INVALID_MESSAGE,void 0):n}return _.rich=v,_.markup=(e,t,r)=>{let n=v(e,t,r);if("string"!=typeof n){let t=new u(l.FORMATTING_ERROR,void 0);return g(t),f({error:t,key:e,namespace:m})}return n},_.raw=e=>{if(E)return f({error:p,key:e,namespace:m});try{return h(d,p,e,m)}catch(t){return y(e,l.MISSING_MESSAGE,t.message)}},_.has=e=>{if(E)return!1;try{return h(d,p,e,m),!0}catch(e){return!1}},_}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:c}=e;function h(e){var t;return null!=(t=e)&&t.timeZone||(c?e={...e,timeZone:c}:s(new u(l.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(l.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new u(l.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=h(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function g(){return a||(s(new u(l.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):g(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=g());let l=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(l)),s.numeric="second"===a?"auto":"always";let u=(n=a,Math.round(l/d[n]));return r.getRelativeTimeFormat(o,s).format(u,a)}catch(t){return s(new u(l.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return p(i,null==n?void 0:n.dateTime,n=>(n=h(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},4792:(e,t,r)=>{"use strict";e.exports=r(6514)},4804:(e,t)=>{"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}Object.defineProperty(t,"__esModule",{value:!0}),t.extends=r},5056:e=>{"use strict";e.exports=import("@ffmpeg/util")},5132:(e,t,r)=>{"use strict";t.IntlContext=r(3210).createContext(void 0)},5185:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3210),o=r(9969),i=r(5132);r(7132);var a=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:s,getMessageFallback:l,locale:u,messages:c,now:h,onError:f,timeZone:d}=e,p=n.useMemo(()=>o.createCache(),[u]),m=n.useMemo(()=>o.createIntlFormatters(p),[p]),g=n.useMemo(()=>({...o.initializeConfig({locale:u,defaultTranslationValues:r,formats:s,getMessageFallback:l,messages:c,now:h,onError:f,timeZone:d}),formatters:m,cache:p}),[p,r,s,m,l,u,c,h,f,d]);return a.default.createElement(i.IntlContext.Provider,{value:g},t)}},5315:(e,t,r)=>{"use strict";e.exports=r(5185)},5327:(e,t,r)=>{"use strict";var n=r(9550);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s},5529:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\src\\\\components\\\\VideoConverter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Billie\\src\\components\\VideoConverter.tsx","default")},5922:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Billie\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Billie\\node_modules\\next-intl\\dist\\esm\\shared\\NextIntlClientProvider.js","default")},6023:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},6189:(e,t,r)=>{"use strict";r.r(t);var n=r(5773),o={};for(let e in n)"default"!==e&&(o[e]=()=>n[e]);r.d(t,o)},6280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return h}});let n=r(2584),o=r(9294),i=r(3033),a=r(4971),s=r(23),l=r(8388),u=r(6926),c=(r(4523),r(8719));function h(){let e=o.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return d(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=f.get(u);if(n)return n;let o=(0,l.makeHangingPromise)(u.renderSignal,"`headers()`");return f.set(u,o),Object.defineProperties(o,{append:{value:function(){let e=`\`headers().append(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),o}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return d((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function d(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function p(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6294:(e,t,r)=>{"use strict";let n=r(3033),o=r(9294),i=r(4971),a=r(6926),s=r(23),l=r(8479);function u(){let e=o.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return c(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return c(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function c(e,t){let r,n=h.get(u);return n||(r=f(e),h.set(e,r),r)}let h=new WeakMap;function f(e){let t=new d(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class d{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=o.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new l.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},6514:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3453);r(3210),r(5132),t.useLocale=n.useLocale},6565:(e,t,r)=>{var n={"./en.json":[7368,368],"./fr.json":[6791,791]};function o(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],o=t[0];return r.e(t[1]).then(()=>r.t(o,19))}o.keys=()=>Object.keys(n),o.id=6565,e.exports=o},6682:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{default:()=>s});var o=r(3210),i=r.n(o),a=r(5185);function s(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return i().createElement(a.IntlProvider,n({locale:t},r))}},6839:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return h},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return s}});let n=r(2836),o=r(9026),i=r(9121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=o.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function h(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7132:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new s}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},7133:(e,t,r)=>{"use strict";var n=r(4727),o=r(5327);r(444),r(1120),r(9550),n.IntlError,n.IntlErrorCode,n.createFormatter,t.gZ=o.createCache,t.CB=o.createIntlFormatters,t.TD=o.initializeConfig,t.HM=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:l=o.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...u,onError:l,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},"!")}},7552:(e,t,r)=>{"use strict";e.exports=r(3674)},7576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return o.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(6897),o=r(9026),i=r(2765),a=r(8976),s=r(899),l=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7840:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(2688).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},8447:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>l,FormatError:()=>eo,IntlMessageFormat:()=>eh,InvalidValueError:()=>ei,InvalidValueTypeError:()=>ea,MissingValueError:()=>es,PART_TYPE:()=>u,default:()=>ef,formatToParts:()=>eu,isFormatXMLElementFn:()=>el});var n,o,i,a,s,l,u,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function d(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,r(7132));function m(e){return e.type===o.literal}function g(e){return e.type===o.number}function b(e){return e.type===o.date}function E(e){return e.type===o.time}function y(e){return e.type===o.select}function v(e){return e.type===o.plural}function _(e){return e.type===o.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var H=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,R=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,N=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,S=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,P=/^(@+)?(\+|#+)?[rs]?$/g,B=/(\*)(0+)|(#+)(0+)|(0+)/g,I=/^(0+)$/;function w(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(P,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function C(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function O(e){var t=C(e);return t||{}}var L={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},M=new RegExp("^".concat(H.source,"*")),x=new RegExp("".concat(H.source,"*$"));function k(e,t){return{start:e,end:t}}var F=!!String.prototype.startsWith&&"_a".startsWith("a",1),D=!!String.fromCodePoint,j=!!Object.fromEntries,G=!!String.prototype.codePointAt,U=!!String.prototype.trimStart,V=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},K=!0;try{K=(null==(a=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){K=!1}var Z=F?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},q=D?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},z=j?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},Y=G?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},W=U?function(e){return e.trimStart()}:function(e){return e.replace(M,"")},$=V?function(e){return e.trimEnd()}:function(e){return e.replace(x,"")};function Q(e,t){return new RegExp(e,t)}if(K){var J=Q("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return J.lastIndex=t,null!=(r=J.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=Y(e,t);if(void 0===o||er(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return q.apply(void 0,r)};var ee=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),i.push({type:o.pound,location:k(l,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&et(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,k(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:k(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,k(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,k(r,this.clonePosition()));if(this.isEOF()||!et(this.char()))return this.error(n.INVALID_TAG,k(l,this.clonePosition()));var u=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,k(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:k(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,k(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var l=k(r,this.clonePosition());return{val:{type:o.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(et(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return q.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),q(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,k(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,k(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:k(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,k(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:k(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,k(l,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var d=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=$(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,k(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:k(d,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var b=k(a,this.clonePosition());if(h&&Z(null==h?void 0:h.style,"::",0)){var E=W(h.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(E,h.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:b,style:p.val},err:null}}if(0===E.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,b);var y,v=E;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(L[t||""]||L[n||""]||L["".concat(n,"-001")]||L["001"])[0]}(t);for(("H"==l||"k"==l)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=l+r}else"J"===o?r+="H":r+=o}return r}(E,this.locale));var m={type:i.dateTime,pattern:v,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},v.replace(R,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===u?o.date:o.time,value:r,location:b,style:m},err:null}}return{val:{type:"number"===u?o.number:"date"===u?o.date:o.time,value:r,location:b,style:null!=(s=null==h?void 0:h.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,k(_,f({},_)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==u&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,k(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=p.val}var H=this.tryParsePluralOrSelectOptions(e,u,t,T);if(H.err)return H;var g=this.tryParseArgumentClose(a);if(g.err)return g;var N=k(a,this.clonePosition());if("select"===u)return{val:{type:o.select,value:r,options:z(H.val),location:N},err:null};return{val:{type:o.plural,value:r,options:z(H.val),offset:A,pluralType:"plural"===u?"cardinal":"ordinal",location:N},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,k(l,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,k(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,k(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(N).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),O(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(B,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(I.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(S.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(S,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),w(o)));continue}if(P.test(n.stem)){t=f(f({},t),w(n.stem));continue}var i=C(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!I.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],l=new Set,u=o.value,c=o.location;;){if(0===u.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=k(h,this.clonePosition()),u=this.message.slice(h.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(a=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,k(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(d);if(m.err)return m;s.push([u,{value:p.val,location:k(d,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,k(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,k(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=k(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Y(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Z(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&er(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function et(e){return e>=97&&e<=122||e>=65&&e<=90}function er(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function en(e,t){void 0===t&&(t={});var r=new ee(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,y(t)||v(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else g(t)&&T(t.style)||(b(t)||E(t))&&A(t.style)?delete t.style.location:_(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var eo=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ei=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,o)||this}return h(t,e),t}(eo),ea=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return h(t,e),t}(eo),es=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return h(t,e),t}(eo);function el(e){return"function"==typeof e}function eu(e,t,r,n,i,a,s){if(1===e.length&&m(e[0]))return[{type:u.literal,value:e[0].value}];for(var c=[],h=0;h<e.length;h++){var f=e[h];if(m(f)){c.push({type:u.literal,value:f.value});continue}if(f.type===o.pound){"number"==typeof a&&c.push({type:u.literal,value:r.getNumberFormat(t).format(a)});continue}var d=f.value;if(!(i&&d in i))throw new es(d,s);var p=i[d];if(f.type===o.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),c.push({type:"string"==typeof p?u.literal:u.object,value:p});continue}if(b(f)){var H="string"==typeof f.style?n.date[f.style]:A(f.style)?f.style.parsedOptions:void 0;c.push({type:u.literal,value:r.getDateTimeFormat(t,H).format(p)});continue}if(E(f)){var H="string"==typeof f.style?n.time[f.style]:A(f.style)?f.style.parsedOptions:n.time.medium;c.push({type:u.literal,value:r.getDateTimeFormat(t,H).format(p)});continue}if(g(f)){var H="string"==typeof f.style?n.number[f.style]:T(f.style)?f.style.parsedOptions:void 0;H&&H.scale&&(p*=H.scale||1),c.push({type:u.literal,value:r.getNumberFormat(t,H).format(p)});continue}if(_(f)){var R=f.children,N=f.value,S=i[N];if(!el(S))throw new ea(N,"function",s);var P=S(eu(R,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(P)||(P=[P]),c.push.apply(c,P.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(y(f)){var B=f.options[p]||f.options.other;if(!B)throw new ei(f.value,p,Object.keys(f.options),s);c.push.apply(c,eu(B.value,t,r,n,i));continue}if(v(f)){var B=f.options["=".concat(p)];if(!B){if(!Intl.PluralRules)throw new eo('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,s);var I=r.getPluralRules(t,{type:f.pluralType}).select(p-(f.offset||0));B=f.options[I]||f.options.other}if(!B)throw new ei(f.value,p,Object.keys(f.options),s);c.push.apply(c,eu(B.value,t,r,n,i,p-(f.offset||0)));continue}}return c.length<2?c:c.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ec(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var eh=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return eu(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=s.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=o||{},c=(l.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(l,["formatters"]));this.ast=e.__parse(t,f(f({},c),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.number),strategy:p.strategies.variadic}),getDateTimeFormat:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.dateTime),strategy:p.strategies.variadic}),getPluralRules:(0,p.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,d([void 0],t,!1)))},{cache:ec(a.pluralRules),strategy:p.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=en,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ef=eh},8591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2101),o=r(9969);r(8447),r(3210),r(7132),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.createTranslator=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:l=o.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...u,onError:l,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},"!")}},8687:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8782:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,I:()=>n});let n=["en","fr"],o=async({requestLocale:e})=>{let t=await e;return t&&n.includes(t)||(t="en"),{locale:t,messages:(await r(6565)(`./${t}.json`)).default}}},8868:()=>{},8976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9053:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(5185),o=r(3453),i=r(3210),a=r(2101);r(9969),r(7132),r(5132),r(8447);let s=!1,l="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=o.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:s,timeZone:l}=o.useIntlContext();return i.useMemo(()=>a.createFormatter({formats:e,locale:r,now:n,onError:s,timeZone:l,_formatters:t}),[e,t,n,r,s,l])},t.useMessages=function(){let e=o.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=o.useIntlContext(),[n,a]=i.useState(r||new Date);return i.useEffect(()=>{if(!t)return;let e=setInterval(()=>{a(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return o.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:u,formats:c,formatters:h,getMessageFallback:f,locale:d,onError:p,timeZone:m}=o.useIntlContext(),g=e["!"],b=a.resolveNamespace(t,"!");return m||s||!l||(s=!0,p(new a.IntlError(a.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),i.useMemo(()=>a.createBaseTranslator({cache:n,formatters:h,getMessageFallback:f,messages:g,defaultTranslationValues:u,namespace:b,onError:p,formats:c,locale:d,timeZone:m}),[n,h,f,g,u,b,p,c,d,m])}({"!":o.useIntlContext().messages},e?"!.".concat(e):"!","!")}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9266:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(4804),o=r(3210),i=r(5315),a=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return a.default.createElement(i.IntlProvider,n.extends({locale:t},r))}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9305:e=>{"use strict";e.exports=import("@ffmpeg/ffmpeg")},9550:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:l,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>u});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),l={create:function(){return new s}},u={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},9916:(e,t,r)=>{"use strict";var n=r(7576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}})},9933:(e,t,r)=>{"use strict";let n=r(4069),o=r(3158),i=r(9294),a=r(3033),s=r(4971),l=r(23),u=r(8388),c=r(6926),h=(r(4523),r(8719)),f=new WeakMap;function d(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):E.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function p(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function E(e){for(let e of this.getAll())this.delete(e.name);return e}},9969:(e,t,r)=>{"use strict";var n=r(7132);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function l(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:l(Intl.DateTimeFormat,e.dateTime),getNumberFormat:l(Intl.NumberFormat,e.number),getPluralRules:l(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:l(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:l(Intl.ListFormat,e.list),getDisplayNames:l(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[825],()=>r(3215));module.exports=n})();