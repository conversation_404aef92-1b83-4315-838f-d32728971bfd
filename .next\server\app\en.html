<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/9af4fd01e4b7f88e.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-0eb0f07c0b8e3b80.js"/><script src="/_next/static/chunks/4bd1b696-f0065bb01fdaba5f.js" async=""></script><script src="/_next/static/chunks/684-a5b4bd9d48ebc5e8.js" async=""></script><script src="/_next/static/chunks/main-app-11643d1287e5abd1.js" async=""></script><script src="/_next/static/chunks/699-416c0b5f86a762f0.js" async=""></script><script src="/_next/static/chunks/app/%5Blocale%5D/page-5c18142be3a734de.js" async=""></script><script src="/_next/static/chunks/app/%5Blocale%5D/layout-5d730d9d8e25eb22.js" async=""></script><link rel="icon" href="/favicon.ico"/><title>Billie WebM Converter</title><meta name="description" content="A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing"/><meta name="author" content="Billie WebM Converter"/><meta name="keywords" content="video converter, webm, ffmpeg, online converter, video compression"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"><div hidden=""><!--$--><!--/$--></div><div class="container mx-auto px-4 py-8"><main><div class="space-y-8"><div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"><div class="flex justify-between items-center mb-6"><div><h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">Billie WebM Converter</h1><p class="text-gray-600 dark:text-gray-300">Convert MP4 videos to WebM format with 512x512 resizing</p></div><button class="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe h-4 w-4"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg>Switch to French</button></div><div class="upload-area p-8 text-center cursor-pointer "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-upload mx-auto h-12 w-12 text-gray-400 mb-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" x2="12" y1="3" y2="15"></line></svg><p class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Drag and drop your video file here</p><p class="text-sm text-gray-500 dark:text-gray-400 mb-2">or</p><p class="text-sm text-blue-600 dark:text-blue-400 mb-4">Click to select file</p><p class="text-xs text-gray-500 dark:text-gray-400">Max file size: 100MB</p><input type="file" accept="video/*" class="hidden"/></div></div><div class="max-w-4xl mx-auto grid md:grid-cols-3 gap-6 mt-12"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center"><div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4"><svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div><h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Fast Processing</h3><p class="text-gray-600 dark:text-gray-400 text-sm">Client-side processing means your videos never leave your device, ensuring privacy and speed.</p></div><div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center"><div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4"><svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div><h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Perfect Sizing</h3><p class="text-gray-600 dark:text-gray-400 text-sm">Automatically resizes videos to 512x512 pixels while preserving aspect ratio with smart padding.</p></div><div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center"><div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4"><svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg></div><h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Privacy First</h3><p class="text-gray-600 dark:text-gray-400 text-sm">All processing happens in your browser. No uploads to servers, no data collection.</p></div></div><div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"><h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">How to Use</h2><ol class="space-y-3 text-gray-600 dark:text-gray-400"><li class="flex items-start gap-3"><span class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">1</span><span>Step 1: Upload your MP4 file</span></li><li class="flex items-start gap-3"><span class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">2</span><span>Step 2: Wait for automatic conversion</span></li><li class="flex items-start gap-3"><span class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">3</span><span>Step 3: Download your new WebM file</span></li><li class="flex items-start gap-3"><span class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium">4</span><span>Download your converted WebM video file</span></li></ol></div></div><!--$--><!--/$--></main></div><script src="/_next/static/chunks/webpack-0eb0f07c0b8e3b80.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n6:I[9665,[],\"OutletBoundary\"]\n9:I[4911,[],\"AsyncMetadataOutlet\"]\nb:I[9665,[],\"ViewportBoundary\"]\nd:I[9665,[],\"MetadataBoundary\"]\nf:I[6614,[],\"\"]\n:HL[\"/_next/static/css/9af4fd01e4b7f88e.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"_9dcwiYmTh5Vy-pSnKxJ_\",\"p\":\"\",\"c\":[\"\",\"en\"],\"i\":false,\"f\":[[[\"\",{\"children\":[[\"locale\",\"en\",\"d\"],{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9af4fd01e4b7f88e.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"locale\",\"en\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,\"$L4\"]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L5\",null,[\"$\",\"$L6\",null,{\"children\":[\"$L7\",\"$L8\",[\"$\",\"$L9\",null,{\"promise\":\"$@a\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"2pw-SPpdBiQ1pCUIkrQq4v\",{\"children\":[[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}],null]}],[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$f\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"10:\"$Sreact.suspense\"\n11:I[4911,[],\"AsyncMetadata\"]\ne:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$10\",null,{\"fallback\":null,\"children\":[\"$\",\"$L11\",null,{\"promise\":\"$@12\"}]}]}]\n8:null\n"])</script><script>self.__next_f.push([1,"13:I[9295,[\"699\",\"static/chunks/699-416c0b5f86a762f0.js\",\"465\",\"static/chunks/app/%5Blocale%5D/page-5c18142be3a734de.js\"],\"default\",1]\n"])</script><script>self.__next_f.push([1,"5:[\"$\",\"div\",null,{\"className\":\"space-y-8\",\"children\":[[\"$\",\"$L13\",null,{}],[\"$\",\"div\",null,{\"className\":\"max-w-4xl mx-auto grid md:grid-cols-3 gap-6 mt-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\"children\":[\"$\",\"svg\",null,{\"className\":\"w-6 h-6 text-blue-600 dark:text-blue-400\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"viewBox\":\"0 0 24 24\",\"children\":[\"$\",\"path\",null,{\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"strokeWidth\":2,\"d\":\"M13 10V3L4 14h7v7l9-11h-7z\"}]}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\"children\":\"Fast Processing\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-400 text-sm\",\"children\":\"Client-side processing means your videos never leave your device, ensuring privacy and speed.\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\"children\":[\"$\",\"svg\",null,{\"className\":\"w-6 h-6 text-green-600 dark:text-green-400\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"viewBox\":\"0 0 24 24\",\"children\":[\"$\",\"path\",null,{\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"strokeWidth\":2,\"d\":\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"}]}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\"children\":\"Perfect Sizing\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-400 text-sm\",\"children\":\"Automatically resizes videos to 512x512 pixels while preserving aspect ratio with smart padding.\"}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4\",\"children\":[\"$\",\"svg\",null,{\"className\":\"w-6 h-6 text-purple-600 dark:text-purple-400\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"viewBox\":\"0 0 24 24\",\"children\":[\"$\",\"path\",null,{\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"strokeWidth\":2,\"d\":\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"}]}]}],[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\",\"children\":\"Privacy First\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 dark:text-gray-400 text-sm\",\"children\":\"All processing happens in your browser. No uploads to servers, no data collection.\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4\",\"children\":\"How to Use\"}],[\"$\",\"ol\",null,{\"className\":\"space-y-3 text-gray-600 dark:text-gray-400\",\"children\":[[\"$\",\"li\",null,{\"className\":\"flex items-start gap-3\",\"children\":[[\"$\",\"span\",null,{\"className\":\"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\"children\":\"1\"}],[\"$\",\"span\",null,{\"children\":\"Step 1: Upload your MP4 file\"}]]}],[\"$\",\"li\",null,{\"className\":\"flex items-start gap-3\",\"children\":[[\"$\",\"span\",null,{\"className\":\"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\"children\":\"2\"}],[\"$\",\"span\",null,{\"children\":\"Step 2: Wait for automatic conversion\"}]]}],[\"$\",\"li\",null,{\"className\":\"flex items-start gap-3\",\"children\":[[\"$\",\"span\",null,{\"className\":\"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\"children\":\"3\"}],[\"$\",\"span\",null,{\"children\":\"Step 3: Download your new WebM file\"}]]}],[\"$\",\"li\",null,{\"className\":\"flex items-start gap-3\",\"children\":[[\"$\",\"span\",null,{\"className\":\"flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium\",\"children\":\"4\"}],[\"$\",\"span\",null,{\"children\":\"Download your converted WebM video file\"}]]}]]}]]}]]}]\n"])</script><script>self.__next_f.push([1,"4:[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}]]}],[\"$\",\"body\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\"children\":\"$L14\"}]]}]\n"])</script><script>self.__next_f.push([1,"15:I[2103,[\"450\",\"static/chunks/app/%5Blocale%5D/layout-5d730d9d8e25eb22.js\"],\"default\"]\n14:[\"$\",\"$L15\",null,{\"locale\":\"en\",\"now\":\"$D2025-06-25T04:37:06.202Z\",\"timeZone\":\"America/Mexico_City\",\"messages\":{\"title\":\"Billie WebM Converter\",\"subtitle\":\"Convert MP4 videos to WebM format with 512x512 resizing\",\"steps\":{\"step1\":\"Step 1: Upload your MP4 file\",\"step2\":\"Step 2: Wait for automatic conversion\",\"step3\":\"Step 3: Download your new WebM file\"},\"upload\":{\"dragDrop\":\"Drag and drop your video file here\",\"or\":\"or\",\"clickToSelect\":\"Click to select file\",\"maxSize\":\"Max file size: 100MB\",\"supportedFormats\":\"Supports MP4, AVI, MOV, MKV, WebM and more\",\"uploading\":\"Uploading...\",\"processing\":\"Converting video...\",\"success\":\"Conversion complete!\",\"downloadReady\":\"Your WebM file is ready for download\"},\"buttons\":{\"selectFile\":\"Select File\",\"download\":\"Download WebM\",\"convertAnother\":\"Convert Another File\",\"cancel\":\"Cancel\"},\"errors\":{\"unsupportedFormat\":\"Unsupported video format. Please upload a common video file (MP4, AVI, MOV, etc.)\",\"fileTooLarge\":\"File size too large. Please upload a video smaller than 100MB.\",\"conversionFailed\":\"Conversion failed. Please try again.\",\"uploadFailed\":\"Upload failed. Please try again.\",\"noFileSelected\":\"No file selected. Please choose a video file.\"},\"language\":{\"switch\":\"Switch to French\",\"current\":\"English\"},\"progress\":{\"uploading\":\"Uploading: {progress}%\",\"converting\":\"Converting: {progress}%\",\"preparing\":\"Preparing conversion...\",\"finalizing\":\"Finalizing...\"}},\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-8\",\"children\":[\"$\",\"main\",null,{\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]\nc:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]"])</script><script>self.__next_f.push([1,"]\n"])</script><script>self.__next_f.push([1,"7:null\n"])</script><script>self.__next_f.push([1,"a:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Billie WebM Converter\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Billie WebM Converter\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"video converter, webm, ffmpeg, online converter, video compression\"}]],\"error\":null,\"digest\":\"$undefined\"}\n12:{\"metadata\":\"$a:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>